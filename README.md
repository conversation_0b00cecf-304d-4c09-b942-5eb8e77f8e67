# identityserver

[![Build Status](https://travis-ci.org/iam/identityserver.svg?branch=master)](https://travis-ci.org/iam/identityserver)

## Documentation

 Documentation is hosted on [gitbook](https://gig.gitbooks.io/iam/content/)

## install and run locally

### Prerequisites

* go 1.7

### Installation

```bash
git clone https://git.gig.tech/gig-meneja/iam
cd identityserver
go generate && go build
./identityserver -d
```

### Run

```
identityserver
```

To see the available options (like changing the default mongo connectionstring), execute `identityserver -h`.

Browse to https://dev.iam.gig.tech:8443

* dev.iam.gig.tech is a public DNS entry that points to 127.0.0.1 and ::1


### Docker-compose

You can run via [Docker-compose](https://docs.docker.com/compose/install/). You will get a running `identityserver` with its own [Mongo](https://hub.docker.com/_/mongo/) docker instance.

```
docker-compose up
```

then browse to https://dev.iam.gig.tech:8443 (assuming docker runs on localhost)

## Contribute

When you want to contribute to the development, follow the [contribution guidelines](contributing.md).
