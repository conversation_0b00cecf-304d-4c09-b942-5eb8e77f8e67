# This file is autogenerated, do not edit; changes may be undone by the next 'dep ensure'.


[[projects]]
  name = "github.com/davecgh/go-spew"
  packages = ["spew"]
  revision = "346938d642f2ec3594ed81d874461961cd0faa76"
  version = "v1.1.0"

[[projects]]
  branch = "master"
  name = "github.com/gin-contrib/sse"
  packages = ["."]
  revision = "22d885f9ecc78bf4ee5d72b937e4bbcdc58e8cae"

[[projects]]
  name = "github.com/gin-gonic/gin"
  packages = [".","binding","render"]
  revision = "d459835d2b077e44f7c9b453505ee29881d5d12d"
  version = "v1.2"

[[projects]]
  name = "github.com/go-redis/redis"
  packages = [".","internal","internal/consistenthash","internal/hashtag","internal/pool","internal/proto"]
  revision = "19c1c2272e00c1aaa903cf574c746cd449f9cd3c"
  version = "v6.5.7"

[[projects]]
  branch = "master"
  name = "github.com/golang/protobuf"
  packages = ["proto"]
  revision = "ab9f9a6dab164b7d1246e0e688b0ab7b94d8553e"

[[projects]]
  name = "github.com/mattn/go-isatty"
  packages = ["."]
  revision = "fc9e8d8ef48496124e79ae0df75490096eccf6fe"
  version = "v0.0.2"

[[projects]]
  name = "github.com/pkg/errors"
  packages = ["."]
  revision = "645ef00459ed84a119197bfb8d8205042c6df63d"
  version = "v0.8.0"

[[projects]]
  name = "github.com/pmezard/go-difflib"
  packages = ["difflib"]
  revision = "792786c7400a136282c1664665ae0a8db921c6c2"
  version = "v1.0.0"

[[projects]]
  name = "github.com/stretchr/testify"
  packages = ["assert","require"]
  revision = "69483b4bd14f5845b5a1e55bca19e954e827f1d0"
  version = "v1.1.4"

[[projects]]
  branch = "master"
  name = "github.com/ugorji/go"
  packages = ["codec"]
  revision = "5efa3251c7f7d05e5d9704a69a984ec9f1386a40"

[[projects]]
  branch = "master"
  name = "golang.org/x/sys"
  packages = ["unix"]
  revision = "43e60d72a8e2bd92ee98319ba9a384a0e9837c08"

[[projects]]
  name = "gopkg.in/go-playground/validator.v8"
  packages = ["."]
  revision = "5f1438d3fca68893a817e4a66806cea46a9e4ebf"
  version = "v8.18.2"

[[projects]]
  branch = "v2"
  name = "gopkg.in/yaml.v2"
  packages = ["."]
  revision = "eb3733d160e74a9c7e442f435eb3bea458e1d19f"

[solve-meta]
  analyzer-name = "dep"
  analyzer-version = 1
  inputs-digest = "0a511933063b3e715bcad7b7d7da2e0590a20d3b8a23786189d955ad79a71a97"
  solver-name = "gps-cdcl"
  solver-version = 1
