{"ImportPath": "git.gig.tech/gig-meneja/iam", "GoVersion": "go1.15", "GodepVersion": "v79", "Deps": [{"ImportPath": "github.com/sirupsen/logrus", "Comment": "v0.8.7", "Rev": "418b41d23a1bf978c06faea5313ba194650ac088"}, {"ImportPath": "github.com/urfave/cli", "Comment": "1.2.0-187-gc31a797", "Rev": "c31a7975863e7810c92e2e288a9ab074f9a88f29"}, {"ImportPath": "github.com/davecgh/go-spew/spew", "Rev": "5215b55f46b2b919f50a1df0eaa5886afe4e3b3d"}, {"ImportPath": "github.com/dgrijalva/jwt-go", "Comment": "v2.6.0-5-g40bd0f3", "Rev": "40bd0f3b4891a9d7f121bfb7b8e8b0525625e262"}, {"ImportPath": "github.com/go-gomail/gomail", "Comment": "2.0.0-23-g81ebce5", "Rev": "81ebce5c23dfd25c6c67194b37d3dd3f338c98b1"}, {"ImportPath": "github.com/gorilla/context", "Rev": "1c83b3eabd45b6d76072b66b746c20815fb2872d"}, {"ImportPath": "github.com/gorilla/handlers", "Rev": "b3aff83722cb2ae031a70cae984650e3a16cd20e"}, {"ImportPath": "github.com/gorilla/mux", "Rev": "26a6070f849969ba72b72256e9f14cf519751690"}, {"ImportPath": "github.com/gorilla/securecookie", "Rev": "68004d2ba3cbc80d3a9949ea431b5c9f22ee8707"}, {"ImportPath": "github.com/gorilla/sessions", "Rev": "f7261893ca3ea922c30eabe742c036d2c1de6e0a"}, {"ImportPath": "github.com/hgfischer/go-otp", "Rev": "38f86955cc6459714c4a5945fc6dfbacdb30b624"}, {"ImportPath": "github.com/justinas/alice", "Rev": "29a850317649fc021f42f80ec29552f9a3156ee9"}, {"ImportPath": "github.com/pkg/errors", "Comment": "v0.8.0-7-gf15c970", "Rev": "f15c970de5b76fac0b59abb32d62c17cc7bed265"}, {"ImportPath": "github.com/pmezard/go-difflib/difflib", "Rev": "792786c7400a136282c1664665ae0a8db921c6c2"}, {"ImportPath": "github.com/stretchr/testify/assert", "Comment": "v1.1.3-19-gd77da35", "Rev": "d77da356e56a7428ad25149ca77381849a6a5232"}, {"ImportPath": "github.com/stretchr/testify/require", "Comment": "v1.1.3-19-gd77da35", "Rev": "d77da356e56a7428ad25149ca77381849a6a5232"}, {"ImportPath": "github.com/ulule/limiter", "Comment": "v2.1.0", "Rev": "0d25c13867d07314999d01525f9d69d40c6bf235"}, {"ImportPath": "github.com/ulule/limiter/drivers/middleware/stdlib", "Comment": "v2.1.0", "Rev": "0d25c13867d07314999d01525f9d69d40c6bf235"}, {"ImportPath": "github.com/ulule/limiter/drivers/store/common", "Comment": "v2.1.0", "Rev": "0d25c13867d07314999d01525f9d69d40c6bf235"}, {"ImportPath": "github.com/ulule/limiter/drivers/store/memory", "Comment": "v2.1.0", "Rev": "0d25c13867d07314999d01525f9d69d40c6bf235"}, {"ImportPath": "gopkg.in/mgo.v2", "Comment": "r2015.06.03-5-g22287ba", "Rev": "22287bab4379e1fbf6002fb4eb769888f3fb224c"}, {"ImportPath": "gopkg.in/mgo.v2/bson", "Comment": "r2015.06.03-5-g22287ba", "Rev": "22287bab4379e1fbf6002fb4eb769888f3fb224c"}, {"ImportPath": "gopkg.in/mgo.v2/internal/sasl", "Comment": "r2015.06.03-5-g22287ba", "Rev": "22287bab4379e1fbf6002fb4eb769888f3fb224c"}, {"ImportPath": "gopkg.in/mgo.v2/internal/scram", "Comment": "r2015.06.03-5-g22287ba", "Rev": "22287bab4379e1fbf6002fb4eb769888f3fb224c"}, {"ImportPath": "gopkg.in/validator.v2", "Rev": "3e4f037f12a1221a0864cf0dd2e81c452ab22448"}]}