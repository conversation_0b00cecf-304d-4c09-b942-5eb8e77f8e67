version: '2'

services:

  identityserver:
    build: .
    environment:
      - SESSION_TIMEOUT=36000
    # entrypoint: ["sh", "-c", "go generate && go build && ./iam -d -c mongodb://mongo:27017/iam"]
    entrypoint: ["sh", "-c", "./iam -d -c mongodb://mongo:27017/iam"]
  nginx:
    build:
      dockerfile: Dockerfile.nginx
      context: .
    ports:
    - "443:443"
  mongo:
    image: mongo
    ports:
      - "27017:27017"
