# FROM ghub.gig.tech/gig-meneja/iam_ui/iam-ui-base:latest
FROM iam_ui
FROM golang:1.22
ARG TOKEN=**************************
ARG GOBINDATAVERSION=a0ff2567cfb70903282db057e799fd826784d41d

RUN go install github.com/go-bindata/go-bindata/...@latest

ARG GORAMLVERSION=a8cc1c065538cfc97ffc077d3a185f653ad406e6

RUN git clone https://gitlab-ci-token:$<EMAIL>/gig-meneja/forks/go-raml.git $GOPATH/src/github.com/Jumpscale/go-raml
WORKDIR $GOPATH/src/github.com/Jumpscale/go-raml
RUN git checkout $GORAMLVERSION
RUN ./build.sh

RUN git clone -b v1.22.0 https://github.com/go-delve/delve $GOPATH/src/github.com/go-delve/delve
WORKDIR $GOPATH/src/github.com/go-delve/delve
RUN go install github.com/go-delve/delve/cmd/dlv

#ENV CGO_ENABLED 0
RUN mkdir -p $GOPATH/src/git.gig.tech/gig-meneja/iam
COPY . $GOPATH/src/git.gig.tech/gig-meneja/iam/
WORKDIR $GOPATH/src/git.gig.tech/gig-meneja/iam

RUN go generate

COPY --from=0 /var/iam $GOPATH/src/git.gig.tech/gig-meneja/iam/website
EXPOSE 8080
EXPOSE 40000
EXPOSE 1541