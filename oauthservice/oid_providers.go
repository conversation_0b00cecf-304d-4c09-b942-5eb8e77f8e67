package oauthservice

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gorilla/mux"
)

// OIDCDiscoveryResponse represents the well-known OpenID Configuration
type OIDCDiscoveryResponse struct {
	Issuer                string   `json:"issuer"`
	AuthorizationEndpoint string   `json:"authorization_endpoint"`
	TokenEndpoint         string   `json:"token_endpoint"`
	UserinfoEndpoint      string   `json:"userinfo_endpoint"`
	JwksURI               string   `json:"jwks_uri"`
	ScopesSupported       []string `json:"scopes_supported"`
	ClaimsSupported       []string `json:"claims_supported"`
}

// DiscoverOIDCConfiguration fetches the OpenID Connect configuration from the well-known endpoint
func DiscoverOIDCConfiguration(issuerURL string) (*OIDCDiscoveryResponse, error) {
	// Ensure the issuer URL doesn't end with a slash
	if issuerURL[len(issuerURL)-1] == '/' {
		issuerURL = issuerURL[:len(issuerURL)-1]
	}

	// Construct the well-known URL
	wellKnownURL := fmt.Sprintf("%s/.well-known/openid-configuration", issuerURL)

	// Fetch the configuration
	resp, err := http.Get(wellKnownURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch OIDC configuration: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to fetch OIDC configuration: status code %d", resp.StatusCode)
	}

	// Parse the response
	var config OIDCDiscoveryResponse
	if err := json.NewDecoder(resp.Body).Decode(&config); err != nil {
		return nil, fmt.Errorf("failed to parse OIDC configuration: %v", err)
	}

	return &config, nil
}

// CreateOIDCProviderHandler handles creation of new OIDC providers
func (service *Service) CreateOIDCProviderHandler(w http.ResponseWriter, r *http.Request) {
	var config OIDCProviderConfig
	if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validation
	if err := validateOIDCConfig(&config); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Validate the issuer URL format
	if err := validateOIDCEndpoints(&config); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Try to discover the OIDC configuration
	_, err := DiscoverOIDCConfiguration(config.Issuer)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to discover OIDC configuration: %v", err), http.StatusBadRequest)
		return
	}

	// Create new manager
	mgr := NewManager(r)

	// Check if provider with same name exists
	existing, err := mgr.ListOIDCProviders(false)
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	for _, p := range existing {
		if p.Name == config.Name {
			http.Error(w, "Provider with this name already exists", http.StatusConflict)
			return
		}
	}

	// Set timestamps
	now := time.Now().Unix()
	config.CreatedAt = now
	config.UpdatedAt = now
	config.Active = true

	if err := mgr.CreateOIDCProvider(&config); err != nil {
		http.Error(w, "Failed to create provider", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(config)
}

// Helper functions for validation
func validateOIDCConfig(config *OIDCProviderConfig) error {
	if config.Name == "" {
		return fmt.Errorf("name is required")
	}
	if config.Issuer == "" {
		return fmt.Errorf("issuer is required")
	}
	if config.ClientID == "" {
		return fmt.Errorf("client ID is required")
	}
	if config.ClientSecret == "" {
		return fmt.Errorf("client secret is required")
	}

	// Mandatory scopes that must be included
	mandatoryScopes := "openid profile email"

	// If no scopes are provided, just use the mandatory ones
	if config.Scope == "" {
		config.Scope = mandatoryScopes
		return nil
	}

	// Create a new string with mandatory_scopes + received_scopes
	combinedScopes := mandatoryScopes + " " + config.Scope

	// Split the string by space
	scopesList := strings.Fields(combinedScopes)

	// Turn the list into a set to lose duplicates
	scopesSet := make(map[string]struct{})
	for _, scope := range scopesList {
		scopesSet[scope] = struct{}{}
	}

	// Turn the set back to string and use it to update config
	uniqueScopes := make([]string, 0, len(scopesSet))
	for scope := range scopesSet {
		uniqueScopes = append(uniqueScopes, scope)
	}

	config.Scope = strings.Join(uniqueScopes, " ")
	return nil
}

// Updated to only validate the issuer URL since we're using well-known endpoint discovery
func validateOIDCEndpoints(config *OIDCProviderConfig) error {
	// Validate URL format for issuer
	if _, err := url.Parse(config.Issuer); err != nil {
		return fmt.Errorf("invalid issuer URL format: %s", config.Issuer)
	}
	return nil
}

// GetOIDCProviderHandler retrieves an OIDC provider by ID
func (service *Service) GetOIDCProviderHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]
	if id == "" {
		http.Error(w, "Provider ID is required", http.StatusBadRequest)
		return
	}

	mgr := NewManager(r)
	provider, err := mgr.GetOIDCProvider(id)
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	if provider == nil {
		http.Error(w, "Provider not found", http.StatusNotFound)
		return
	}

	json.NewEncoder(w).Encode(provider)
}

// ListOIDCProvidersHandler returns all OIDC providers
func (service *Service) ListOIDCProvidersHandler(w http.ResponseWriter, r *http.Request) {
	onlyActive := r.URL.Query().Get("active") == "true"

	mgr := NewManager(r)
	providers, err := mgr.ListOIDCProviders(onlyActive)
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Ensure we always return a results object with an array (even if empty)
	response := map[string]interface{}{
		"results": providers,
	}

	json.NewEncoder(w).Encode(response)
}

// UpdateOIDCProviderHandler updates an existing OIDC provider
func (service *Service) UpdateOIDCProviderHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]
	if id == "" {
		http.Error(w, "Provider ID is required", http.StatusBadRequest)
		return
	}

	var config OIDCProviderConfig
	if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if err := validateOIDCConfig(&config); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if err := validateOIDCEndpoints(&config); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Try to discover the OIDC configuration
	_, err := DiscoverOIDCConfiguration(config.Issuer)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to discover OIDC configuration: %v", err), http.StatusBadRequest)
		return
	}

	mgr := NewManager(r)
	config.ID = id
	config.UpdatedAt = time.Now().Unix()

	if err := mgr.UpdateOIDCProvider(&config); err != nil {
		http.Error(w, "Failed to update provider", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(config)
}

// DeleteOIDCProviderHandler removes an OIDC provider
func (service *Service) DeleteOIDCProviderHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]
	if id == "" {
		http.Error(w, "Provider ID is required", http.StatusBadRequest)
		return
	}

	mgr := NewManager(r)

	// First check password login status
	passwordStatus, err := mgr.GetPasswordLoginStatus()
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// If password login is disabled, check for other active providers
	if !passwordStatus.Enabled {
		activeProviders, err := mgr.ListOIDCProviders(true)
		if err != nil {
			http.Error(w, "Internal server error", http.StatusInternalServerError)
			return
		}

		// If this is the only active provider and password login is disabled, prevent deactivation
		if len(activeProviders) == 1 && activeProviders[0].ID == id {
			http.Error(w, "Cannot delete the last authentication method. Enable password login or another provider first.", http.StatusBadRequest)
			return
		}
	}

	if err := mgr.DeleteOIDCProvider(id); err != nil {
		http.Error(w, "Failed to delete provider", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

// ActivateOIDCProviderHandler sets a provider as active
func (service *Service) ActivateOIDCProviderHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]
	if id == "" {
		http.Error(w, "Provider ID is required", http.StatusBadRequest)
		return
	}

	mgr := NewManager(r)

	// Check if provider exists
	provider, err := mgr.GetOIDCProvider(id)
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	if provider == nil {
		http.Error(w, "Provider not found", http.StatusNotFound)
		return
	}

	if err := mgr.ActivateOIDCProvider(id); err != nil {
		http.Error(w, "Failed to activate provider", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// DeactivateOIDCProviderHandler sets a provider as inactive
func (service *Service) DeactivateOIDCProviderHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]
	if id == "" {
		http.Error(w, "Provider ID is required", http.StatusBadRequest)
		return
	}

	mgr := NewManager(r)

	// Check if provider exists
	provider, err := mgr.GetOIDCProvider(id)
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	if provider == nil {
		http.Error(w, "Provider not found", http.StatusNotFound)
		return
	}

	// First check password login status
	passwordStatus, err := mgr.GetPasswordLoginStatus()
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// If password login is disabled, check for other active providers
	if !passwordStatus.Enabled {
		activeProviders, err := mgr.ListOIDCProviders(true)
		if err != nil {
			http.Error(w, "Internal server error", http.StatusInternalServerError)
			return
		}

		// If this is the only active provider and password login is disabled, prevent deactivation
		if len(activeProviders) == 1 && activeProviders[0].ID == id {
			http.Error(w, "Cannot deactivate the last authentication method. Enable password login or another provider first.", http.StatusBadRequest)
			return
		}
	}

	if err := mgr.DeactivateOIDCProvider(id); err != nil {
		http.Error(w, "Failed to deactivate provider", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// PublicOIDCProvider represents the limited information returned for public OIDC providers
type PublicOIDCProvider struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// ListPublicOIDCProvidersHandler returns only active OIDC providers with limited information
func (service *Service) ListPublicOIDCProvidersHandler(w http.ResponseWriter, r *http.Request) {
	mgr := NewManager(r)

	// Only get active providers
	providers, err := mgr.ListOIDCProviders(true)
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Convert to public providers with limited information
	publicProviders := make([]PublicOIDCProvider, 0, len(providers))
	for _, provider := range providers {
		publicProviders = append(publicProviders, PublicOIDCProvider{
			ID:   provider.ID,
			Name: provider.Name,
		})
	}

	// Ensure we always return a results object with an array (even if empty)
	response := map[string]interface{}{
		"results": publicProviders,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
