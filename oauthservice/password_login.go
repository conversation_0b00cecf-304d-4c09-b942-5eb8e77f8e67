package oauthservice

import (
	"encoding/json"
	"net/http"

	log "github.com/sirupsen/logrus"
)

// PasswordLoginStatus represents the enablement status of password login
type PasswordLoginStatus struct {
	Enabled   bool  `json:"enabled" bson:"enabled"`
	UpdatedAt int64 `json:"updatedAt" bson:"updatedAt"`
}

// EnablePasswordLoginHandler handles the API request to enable password login
func (service *Service) EnablePasswordLoginHandler(w http.ResponseWriter, r *http.Request) {

	mgr := NewManager(r)
	err := mgr.EnablePasswordLogin()
	if err != nil {
		log.Error("Error enabling password login:", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)

}

// DisablePasswordLoginHandler handles the API request to disable password login
func (service *Service) DisablePasswordLoginHandler(w http.ResponseWriter, r *http.Request) {
	mgr := NewManager(r)

	activeProviders, err := mgr.ListOIDCProviders(true)
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// If there are no active providers, prevent disabling password login
	if len(activeProviders) == 0 {
		http.Error(w, "Cannot disable password login. Enable another provider first.", http.StatusBadRequest)
		return
	}

	err = mgr.DisablePasswordLogin()
	if err != nil {
		log.Error("Error disabling password login:", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// GetPasswordLoginStatusHandler handles the API request to get the current password login status
func (service *Service) GetPasswordLoginStatusHandler(w http.ResponseWriter, r *http.Request) {
	mgr := NewManager(r)
	status, err := mgr.GetPasswordLoginStatus()
	if err != nil {
		log.Error("Error getting password login status:", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"enabled":   status.Enabled,
		"updatedAt": status.UpdatedAt,
	})
}
