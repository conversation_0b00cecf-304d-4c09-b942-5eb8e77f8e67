package oauthservice

import (
	"crypto/rand"
	"encoding/base64"
	"os"

	"time"

	"git.gig.tech/gig-meneja/iam/db"
	log "github.com/sirupsen/logrus"
)

// Oauth2Client is an oauth2 client
type Oauth2Client struct {
	ClientID                   string
	Label                      string //Label is a just a tag to identity the secret for this ClientID
	Secret                     string
	CallbackURL                string
	ClientCredentialsGrantType bool //ClientCredentialsGrantType indicates if this client can be used in an oauth2 client credentials grant flow
	UserName                   string
}

// NewOauth2Client creates a new NewOauth2Client with a random secret
func NewOauth2Client(clientID, label, callbackURL string, clientCredentialsGrantType bool, username string) *Oauth2Client {
	c := &Oauth2Client{
		ClientID:                   clientID,
		Label:                      label,
		CallbackURL:                callbackURL,
		ClientCredentialsGrantType: clientCredentialsGrantType,
		UserName:                   username,
	}

	randombytes := make([]byte, 39) //Multiple of 3 to make sure no padding is added
	rand.Read(randombytes)
	c.Secret = base64.URLEncoding.EncodeToString(randombytes)
	return c
}

// OIDCProviderConfig represents an OpenID Connect provider configuration
type OIDCProviderConfig struct {
	ID           string `bson:"_id,omitempty" json:"id"`
	Name         string `bson:"name" json:"name"`
	Issuer       string `bson:"issuer" json:"issuer"`
	ClientID     string `bson:"clientId" json:"clientId"`
	ClientSecret string `bson:"clientSecret" json:"clientSecret"`
	Scope        string `bson:"scope" json:"scope"`
	ClaimKey     string `bson:"claimKey,omitempty" json:"claimKey,omitempty"`
	ClaimValue   string `bson:"claimValue,omitempty" json:"claimValue,omitempty"`
	Active       bool   `bson:"active" json:"active"`
	CreatedAt    int64  `bson:"createdAt" json:"createdAt"`
	UpdatedAt    int64  `bson:"updatedAt" json:"updatedAt"`
}

func NewOIDCProviderConfig(name, issuer, clientID, clientSecret, tokenEndpoint, userinfoEndpoint, jwksURI string) *OIDCProviderConfig {
	now := time.Now().Unix()
	return &OIDCProviderConfig{
		Name:         name,
		Issuer:       issuer,
		ClientID:     clientID,
		ClientSecret: clientSecret,
		Scope:        "openid profile email",
		Active:       true,
		CreatedAt:    now,
		UpdatedAt:    now,
	}
}

// getZammadConfig retrieves Zammad configuration from environment variables or uses defaults.
func getZammadConfig() (clientID, secret, zammadURL string) {
	clientID = os.Getenv("ZAMMAD_CLIENT_ID")
	secret = os.Getenv("ZAMMAD_SECRET")
	zammadURL = os.Getenv("ZAMMAD_URL")

	if clientID == "" {
		clientID = "zammad"
		log.Warn("ZAMMAD_CLIENT_ID environment variable not set, using default 'zammad'.")
	}
	if secret == "" {
		log.Error("ZAMMAD_SECRET environment variable not set. Cannot register Zammad client securely without it.")
		secret = "zammad_secret" // Consider if a default secret is appropriate or should cause an error
	}
	if zammadURL == "" {
		log.Error("ZAMMAD_URL environment variable not set. Cannot register Zammad client without it.")
		zammadURL = "https://tickets.whitesky.cloud"
	}
	return clientID, secret, zammadURL
}

// AddZammadClient registers the Zammad OAuth2 client with default settings
func (service *Service) AddZammadClient() error {

	// Get configuration
	zammadClientID, zammadSecret, zammadURL := getZammadConfig()

	// Create a new client manager
	mgr := NewSessionManager(db.GetSession())

	// Check if client already exists
	clients, err := mgr.AllByClientID(zammadClientID)
	if err != nil {
		return err
	}
	if len(clients) > 0 {
		// Client exists, check if URL needs to be updated
		existingClient := clients[0]
		existingURL := existingClient.CallbackURL[:len(existingClient.CallbackURL)-len("/auth/openid_connect/callback")]
		if existingURL != zammadURL {
			// Update the client with new URL
			existingClient.CallbackURL = zammadURL + "/auth/openid_connect/callback"
			err = mgr.UpdateClient(existingClient.ClientID, existingClient.Label, existingClient.Label, existingClient.CallbackURL, existingClient.ClientCredentialsGrantType)
			if err != nil {
				return err
			}
			log.Infof("Zammad OAuth client updated with new URL: %s", zammadURL)
		}
		return nil
	}
	client := &Oauth2Client{
		ClientID:                   zammadClientID,
		Label:                      "Zammad Help Desk",
		Secret:                     zammadSecret,
		CallbackURL:                zammadURL + "/auth/openid_connect/callback",
		ClientCredentialsGrantType: true,
		UserName:                   "builtin:system",
	}

	// Save the client
	err = mgr.CreateClient(client)
	if err != nil {
		return err
	}

	log.Infof("Zammad OAuth client registered with secret: %s", client.Secret)
	return nil
}
