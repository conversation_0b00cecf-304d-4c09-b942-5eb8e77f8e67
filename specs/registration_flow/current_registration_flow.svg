<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="2306px" preserveAspectRatio="none" style="width:944px;height:2306px;" version="1.1" viewBox="0 0 944 2306" width="944px" zoomAndPan="magnify"><defs><filter height="300%" id="fhq0oj10chnkq" width="300%" x="-1" y="-1"><feGaussianBlur result="blurOut" stdDeviation="2.0"/><feColorMatrix in="blurOut" result="blurOut2" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 .4 0"/><feOffset dx="4.0" dy="4.0" in="blurOut2" result="blurOut3"/><feBlend in="SourceGraphic" in2="blurOut3" mode="normal"/></filter></defs><g><ellipse cx="469" cy="18" fill="#000000" filter="url(#fhq0oj10chnkq)" rx="10" ry="10" style="stroke: none; stroke-width: 1.0;"/><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="50" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="127" x="405.5" y="2165"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="405.5" x2="532.5" y1="2191.2969" y2="2191.2969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="107" x="415.5" y="2182.9951">UserIsLoggedIn</text><ellipse cx="469" cy="2285" fill="none" filter="url(#fhq0oj10chnkq)" rx="10" ry="10" style="stroke: #000000; stroke-width: 1.0;"/><ellipse cx="469.5" cy="2285.5" fill="#000000" rx="6" ry="6" style="stroke: none; stroke-width: 1.0;"/><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="2017.2969" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="926" x="6" y="88"/><rect fill="#FFFFFF" height="1985" rx="12.5" ry="12.5" style="stroke: #FFFFFF; stroke-width: 1.0;" width="920" x="9" y="117.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="6" x2="932" y1="114.2969" y2="114.2969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="107" x="415.5" y="105.9951">RegisterScreen</text><ellipse cx="365" cy="137.2969" fill="#000000" filter="url(#fhq0oj10chnkq)" rx="10" ry="10" style="stroke: none; stroke-width: 1.0;"/><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="106.1406" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="147" x="291.5" y="184.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="291.5" x2="438.5" y1="210.5938" y2="210.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="111" x="309.5" y="202.292">BasicInfoScreen</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="127" x="296.5" y="226.7324">Firstname Lastname</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="88" x="296.5" y="240.7012">phonenumber</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="84" x="296.5" y="254.6699">emailaddress</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="60" x="296.5" y="268.6387">password</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="106" x="296.5" y="282.6074">confirmpassword</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="106.1406" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="140" x="208" y="381.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="208" x2="348" y1="407.5938" y2="407.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="94" x="231" y="399.292">BasicInfoValid</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="42" x="213" y="423.7324">Check:</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="62" x="245" y="437.7012">Firstname</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="61" x="245" y="451.6699">Lastname</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="88" x="245" y="465.6387">phonenumber</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="84" x="245" y="479.6074">emailaddress</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="50.2656" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="214" x="171" y="609.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="171" x2="385" y1="635.5938" y2="635.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="163" x="196.5" y="627.292">GetRegistrationSession</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="194" x="176" y="651.7324">Creates new if it does not exist</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="50.2656" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="268" x="144" y="696.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="144" x2="412" y1="722.5938" y2="722.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="120" x="218" y="714.292">LoadUserFromDb</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="248" x="149" y="738.7324">Use username from registrationsession</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="50" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="173" x="73.5" y="799.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="73.5" x2="246.5" y1="825.5938" y2="825.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="153" x="83.5" y="817.292">CreateNewUserObject</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="50" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="147" x="264.5" y="799.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="264.5" x2="411.5" y1="825.5938" y2="825.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="127" x="274.5" y="817.292">UpdateUserObject</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="50" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="204" x="236" y="902.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="236" x2="440" y1="928.5938" y2="928.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="184" x="246" y="920.292">UpsertUsernamePassword</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="92.1719" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="282" x="197" y="989.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="197" x2="479" y1="1015.5938" y2="1015.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="155" x="260.5" y="1007.292">CheckValidatingPhone</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="234" x="202" y="1031.7324">Start phone validation if number from</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="262" x="202" y="1045.7012">basic info screen does not match number</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="230" x="202" y="1059.6699">from registration session cookie and</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="200" x="202" y="1073.6387">set cookie value to new number</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="92.1719" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="267" x="204.5" y="1118.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="204.5" x2="471.5" y1="1144.5938" y2="1144.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="146" x="265" y="1136.292">CheckValidatingEmail</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="212" x="209.5" y="1160.7324">Start email validation if email from</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="247" x="209.5" y="1174.7012">basic info screen does not match email</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="230" x="209.5" y="1188.6699">from registration session cookie and</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="200" x="209.5" y="1202.6387">set cookie value to new number</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="64.2344" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="169" x="253.5" y="1263.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="253.5" x2="422.5" y1="1289.5938" y2="1289.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="128" x="274" y="1281.292">ValidateInfoScreen</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="75" x="258.5" y="1305.7324">phone code</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="149" x="258.5" y="1319.7012">email verification status</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="50" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="231" x="427.5" y="1380.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="427.5" x2="658.5" y1="1406.5938" y2="1406.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="211" x="437.5" y="1398.292">GetRegistrationSessionCookie</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="50" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="190" x="422" y="1483.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="422" x2="612" y1="1509.5938" y2="1509.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="170" x="432" y="1501.292">CheckIfPhoneIsValidated</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="50.2656" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="188" x="374" y="1586.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="374" x2="562" y1="1612.5938" y2="1612.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="111" x="412.5" y="1604.292">CheckSMSCode</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="168" x="379" y="1628.7324">From the validate info form</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="78.2031" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="394" x="239" y="1704.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="239" x2="633" y1="1730.5938" y2="1730.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="224" x="324" y="1722.292">CheckIfValidatedEmailIsRequired</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="149" x="244" y="1746.7324">No clientid query param</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="237" x="244" y="1760.7012">user:validated:email scope requested</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="374" x="244" y="1774.6699">requirevalidatedemail queryparam set with non empty value</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="64.2344" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="222" x="152" y="1835.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="152" x2="374" y1="1861.5938" y2="1861.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="161" x="182.5" y="1853.292">CheckIfEmailIsValidated</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="129" x="157" y="1877.7324">Must be validated by</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="202" x="157" y="1891.7012">pressing the button in the email</text><rect fill="#FEFECE" filter="url(#fhq0oj10chnkq)" height="50" rx="12.5" ry="12.5" style="stroke: #A80036; stroke-width: 1.5;" width="250" x="236" y="1952.2969"/><line style="stroke: #A80036; stroke-width: 1.5;" x1="236" x2="486" y1="1978.5938" y2="1978.5938"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="230" x="246" y="1970.292">DeleteRegistrationSessionValues</text><ellipse cx="361" cy="2080.2969" fill="none" filter="url(#fhq0oj10chnkq)" rx="10" ry="10" style="stroke: #000000; stroke-width: 1.0;"/><ellipse cx="361.5" cy="2080.7969" fill="#000000" rx="6" ry="6" style="stroke: none; stroke-width: 1.0;"/><!--link *start*RegisterScreen to BasicInfoScreen--><path d="M365,147.3369 C365,154.9669 365,166.5969 365,178.9569 " fill="none" id="*start*RegisterScreen-BasicInfoScreen" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="365,184.2869,369,175.2869,365,179.2869,361,175.2869,365,184.2869" style="stroke: #A80036; stroke-width: 1.0;"/><!--link BasicInfoScreen to BasicInfoValid--><path d="M292.849,290.4169 C287.109,296.0869 281.718,302.0769 277,308.2969 C262.617,327.2669 260.745,352.9369 263.302,376.0769 " fill="none" id="BasicInfoScreen-BasicInfoValid" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="263.955,381.2769,266.7918,371.8454,263.3264,376.3165,258.8552,372.8512,263.955,381.2769" style="stroke: #A80036; stroke-width: 1.0;"/><!--link BasicInfoScreen to BasicInfoValid--><path d="M322.567,295.0869 C316.041,305.4569 309.868,316.4369 305,327.2969 C297.428,344.1969 291.708,363.5969 287.526,381.1469 " fill="none" id="BasicInfoScreen-BasicInfoValid" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="325.382,290.6869,317.1711,296.1257,322.6942,294.903,323.9169,300.4261,325.382,290.6869" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="119" x="306" y="340.3638">Basic info not valid</text><!--link BasicInfoValid to GetRegistrationSession--><path d="M278,487.3169 C278,524.5769 278,573.3969 278,604.0569 " fill="none" id="BasicInfoValid-GetRegistrationSession" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="278,609.1869,282,600.1869,278,604.1869,274,600.1869,278,609.1869" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="94" x="296.5" y="537.3638">Basic info valid</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="129" x="279" y="552.4966">Generate username</text><!--link GetRegistrationSession to LoadUserFromDb--><path d="M278,659.4269 C278,669.2069 278,680.5969 278,690.8569 " fill="none" id="GetRegistrationSession-LoadUserFromDb" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="278,696.1069,282,687.1069,278,691.1069,274,687.1069,278,696.1069" style="stroke: #A80036; stroke-width: 1.0;"/><!--link LoadUserFromDb to CreateNewUserObject--><path d="M230.529,746.5069 C221.962,751.8169 213.409,757.8169 206,764.2969 C195.813,773.1969 186.3,784.6269 178.599,795.0469 " fill="none" id="LoadUserFromDb-CreateNewUserObject" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="175.63,799.1369,184.1467,794.1905,178.5612,795.0862,177.6655,789.5007,175.63,799.1369" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="93" x="207" y="777.3638">user not found</text><!--link LoadUserFromDb to UpdateUserObject--><path d="M292.371,746.4869 C300.917,760.8769 311.811,779.2069 320.81,794.3569 " fill="none" id="LoadUserFromDb-UpdateUserObject" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="323.538,798.9569,322.3744,789.177,320.9817,794.6597,315.499,793.2671,323.538,798.9569" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="69" x="313" y="777.3638">user exists</text><!--link CreateNewUserObject to UpsertUsernamePassword--><path d="M202.635,849.4869 C229.178,864.5469 263.354,883.9369 290.725,899.4769 " fill="none" id="CreateNewUserObject-UpsertUsernamePassword" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="295.404,902.1269,289.5432,894.2116,291.0531,899.6633,285.6014,901.1731,295.404,902.1269" style="stroke: #A80036; stroke-width: 1.0;"/><!--link UpdateUserObject to UpsertUsernamePassword--><path d="M338,849.4869 C338,863.6269 338,881.5769 338,896.5769 " fill="none" id="UpdateUserObject-UpsertUsernamePassword" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="338,901.9569,342,892.9569,338,896.9569,334,892.9569,338,901.9569" style="stroke: #A80036; stroke-width: 1.0;"/><!--link UpsertUsernamePassword to BasicInfoScreen--><path d="M385.119,902.2869 C411.844,884.9969 440,858.8169 440,825.2969 C440,334.7969 440,334.7969 440,334.7969 C440,320.4369 434.757,306.9169 427.043,294.8069 " fill="none" id="UpsertUsernamePassword-BasicInfoScreen" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="424.094,290.4169,425.7627,300.1233,426.8695,294.5758,432.417,295.6826,424.094,290.4169" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="104" x="441" y="587.3638">Password invalid</text><!--link UpsertUsernamePassword to CheckValidatingPhone--><path d="M338,952.3469 C338,961.7769 338,972.9669 338,984.0069 " fill="none" id="UpsertUsernamePassword-CheckValidatingPhone" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="338,989.0569,342,980.0569,338,984.0569,334,980.0569,338,989.0569" style="stroke: #A80036; stroke-width: 1.0;"/><!--link CheckValidatingPhone to CheckValidatingEmail--><path d="M338,1081.4269 C338,1091.6009 338,1102.4589 338,1112.8329 " fill="none" id="CheckValidatingPhone-CheckValidatingEmail" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="338,1117.8809,342,1108.8809,338,1112.8809,334,1108.8809,338,1117.8809" style="stroke: #A80036; stroke-width: 1.0;"/><!--link CheckValidatingEmail to ValidateInfoScreen--><path d="M338,1210.4149 C338,1225.9449 338,1243.1279 338,1257.8769 " fill="none" id="CheckValidatingEmail-ValidateInfoScreen" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="338,1263.1929,342,1254.1929,338,1258.1929,334,1254.1929,338,1263.1929" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="206" x="339" y="1241.3638">Save registration session cookie</text><!--link ValidateInfoScreen to BasicInfoScreen--><path d="M253.241,1277.5939 C158.138,1256.3659 17,1216.2739 17,1165.2969 C17,334.7969 17,334.7969 17,334.7969 C17,279.3069 183.381,254.4169 286.054,244.3369 " fill="none" id="ValidateInfoScreen-BasicInfoScreen" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="291.198,243.8469,281.8544,240.733,286.2213,244.3288,282.6255,248.6957,291.198,243.8469" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="107" x="18" y="725.8638">Basic info button</text><!--link ValidateInfoScreen to GetRegistrationSessionCookie--><path d="M386.318,1327.4339 C404.967,1338.9499 426.674,1351.7559 447,1362.2969 C457.457,1367.7199 468.765,1373.0869 479.826,1378.0789 " fill="none" id="ValidateInfoScreen-GetRegistrationSessionCookie" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="484.547,1380.1939,477.9691,1372.8637,479.984,1378.1495,474.6982,1380.1644,484.547,1380.1939" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="98" x="448" y="1358.3638">Register button</text><!--link GetRegistrationSessionCookie to BasicInfoScreen--><path d="M549.397,1380.0399 C554.571,1358.2769 561,1325.3799 561,1296.2969 C561,334.7969 561,334.7969 561,334.7969 C561,279.8669 498.269,256.2069 443.785,246.0069 " fill="none" id="GetRegistrationSessionCookie-BasicInfoScreen" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="438.541,245.0669,446.69,250.598,443.4619,245.9526,448.1073,242.7245,438.541,245.0669" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="179" x="562" y="777.3638">Registration session expired</text><!--link GetRegistrationSessionCookie to CheckIfPhoneIsValidated--><path d="M536.772,1430.4889 C533.101,1444.7509 528.43,1462.8959 524.55,1477.9689 " fill="none" id="GetRegistrationSessionCookie-CheckIfPhoneIsValidated" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="523.267,1482.9519,529.3835,1475.2325,524.5129,1478.1096,521.6359,1473.239,523.267,1482.9519" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="197" x="533" y="1461.3638">Load saved values from cookie</text><!--link CheckIfPhoneIsValidated to CheckSMSCode--><path d="M505.263,1533.4889 C498.284,1547.8749 489.388,1566.2119 482.038,1581.3609 " fill="none" id="CheckIfPhoneIsValidated-CheckSMSCode" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="479.811,1585.9519,487.3387,1579.601,481.9938,1581.4535,480.1413,1576.1086,479.811,1585.9519" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="151" x="497" y="1564.3638">phone not validated yet</text><!--link CheckSMSCode to ValidateInfoScreen--><path d="M449.779,1586.1049 C438.752,1571.1479 424.622,1551.4099 413,1533.2969 C394.185,1503.9739 386.446,1497.8379 374,1465.2969 C357.096,1421.0989 347.355,1367.2029 342.368,1332.3419 " fill="none" id="CheckSMSCode-ValidateInfoScreen" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="341.667,1327.3399,338.9582,1336.8089,342.3627,1332.2912,346.8804,1335.6958,341.667,1327.3399" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="149" x="375" y="1461.3638">No code or invalid code</text><!--link CheckSMSCode to BasicInfoScreen--><path d="M562.23,1595.7599 C646.565,1580.0079 757,1551.3509 757,1509.2969 C757,334.7969 757,334.7969 757,334.7969 C757,270.9169 558.239,249.0769 443.78,241.8069 " fill="none" id="CheckSMSCode-BasicInfoScreen" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="438.732,241.4869,447.4638,246.0427,443.7222,241.8,447.9648,238.0584,438.732,241.4869" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="140" x="758" y="880.3638">Validation key expired</text><!--link CheckSMSCode to CheckIfValidatedEmailIsRequired--><path d="M462.056,1636.4439 C457.722,1654.0509 451.733,1678.3819 446.589,1699.2779 " fill="none" id="CheckSMSCode-CheckIfValidatedEmailIsRequired" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="445.37,1704.2319,451.4043,1696.4481,446.5645,1699.3767,443.636,1694.5369,445.37,1704.2319" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="109" x="471.5" y="1667.3638">SMSCode is valid</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="134" x="459" y="1682.4966">Number is confirmed</text><!--link CheckIfValidatedEmailIsRequired to CheckIfEmailIsValidated--><path d="M281.059,1782.4739 C274.701,1787.6899 268.94,1793.5999 264,1800.2969 C257.744,1808.7769 255.854,1819.6799 255.999,1830.1719 " fill="none" id="CheckIfValidatedEmailIsRequired-CheckIfEmailIsValidated" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="256.224,1835.2769,259.8241,1826.1096,256.004,1830.2817,251.8318,1826.4616,256.224,1835.2769" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="156" x="265" y="1813.3638">Validated email required</text><!--link CheckIfEmailIsValidated to ValidateInfoScreen--><path d="M239.157,1835.0139 C223.538,1811.3999 206,1777.4939 206,1744.2969 C206,1404.2969 206,1404.2969 206,1404.2969 C206,1371.9749 230.001,1347.5809 257.534,1330.2299 " fill="none" id="CheckIfEmailIsValidated-ValidateInfoScreen" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="261.98,1327.5059,252.2164,1328.7988,257.7171,1330.1188,256.397,1335.6195,261.98,1327.5059" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="146" x="207" y="1564.3638">Email not validated yet</text><!--link CheckIfValidatedEmailIsRequired to DeleteRegistrationSessionValues--><path d="M442.374,1782.4149 C447.152,1822.8939 448.903,1887.3849 422,1934.2969 C418.952,1939.6129 414.924,1944.4039 410.388,1948.6839 " fill="none" id="CheckIfValidatedEmailIsRequired-DeleteRegistrationSessionValues" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="406.469,1952.1579,415.8576,1949.1823,410.211,1948.8416,410.5516,1943.1951,406.469,1952.1579" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="181" x="447" y="1871.8638">Validated email not required</text><!--link CheckIfEmailIsValidated to DeleteRegistrationSessionValues--><path d="M282.144,1899.5629 C289.832,1911.0319 299.194,1923.7709 309,1934.2969 C313.602,1939.2359 318.797,1944.0989 324.079,1948.6659 " fill="none" id="CheckIfEmailIsValidated-DeleteRegistrationSessionValues" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="328.126,1952.0969,323.8485,1943.2254,324.3124,1948.8632,318.6746,1949.3271,328.126,1952.0969" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="111" x="310" y="1930.3638">Email is validated</text><!--link DeleteRegistrationSessionValues to *end*RegisterScreen--><path d="M361,2002.4889 C361,2021.9937 361,2048.7628 361,2065.0012 " fill="none" id="DeleteRegistrationSessionValues-*end*RegisterScreen" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="361,2070.0787,365,2061.0787,361,2065.0787,357,2061.0787,361,2070.0787" style="stroke: #A80036; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="306" x="362" y="2033.3638">Set redirectparams value in registration session</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="206" x="412" y="2048.4966">Save registration session cookie</text><!--link *start to RegisterScreen--><path d="M469,28.31 C469,38.66 469,57.32 469,82.54 " fill="none" id="*start-RegisterScreen" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="469,87.67,473,78.67,469,82.67,465,78.67,469,87.67" style="stroke: #A80036; stroke-width: 1.0;"/><!--link RegisterScreen to UserIsLoggedIn--><path d="M469,2105.164 C469,2127.357 469,2145.806 469,2159.695 " fill="none" id="RegisterScreen-UserIsLoggedIn" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="469,2164.983,473,2155.983,469,2159.983,465,2155.983,469,2164.983" style="stroke: #A80036; stroke-width: 1.0;"/><!--link UserIsLoggedIn to *end--><path d="M469,2215.4347 C469,2232.5384 469,2254.827 469,2269.2736 " fill="none" id="UserIsLoggedIn-*end" style="stroke: #A80036; stroke-width: 1.0;"/><polygon fill="#A80036" points="469,2274.5817,473,2265.5817,469,2269.5817,465,2265.5817,469,2274.5817" style="stroke: #A80036; stroke-width: 1.0;"/><!--
@startuml current_registration_flow

[*] - -> RegisterScreen
state RegisterScreen {
    [*] - -> BasicInfoScreen
    BasicInfoScreen: Firstname Lastname\nphonenumber\nemailaddress\npassword\nconfirmpassword
    BasicInfoScreen - -> BasicInfoValid
    BasicInfoValid: Check:\n\tFirstname\n\tLastname\n\tphonenumber\n\temailaddress
    BasicInfoValid -up- -> BasicInfoScreen: Basic info not valid

    BasicInfoValid -d- -> GetRegistrationSession: Basic info valid\nGenerate username
    GetRegistrationSession: Creates new if it does not exist

    GetRegistrationSession - -> LoadUserFromDb
    LoadUserFromDb: Use username from registrationsession
   
    LoadUserFromDb - -> CreateNewUserObject: user not found
    LoadUserFromDb - -> UpdateUserObject: user exists

    CreateNewUserObject - -> UpsertUsernamePassword
    UpdateUserObject - -> UpsertUsernamePassword

    UpsertUsernamePassword - -> BasicInfoScreen: Password invalid
    
    UpsertUsernamePassword - -> CheckValidatingPhone
    CheckValidatingPhone: Start phone validation if number from\nbasic info screen does not match number\nfrom registration session cookie and\nset cookie value to new number

    CheckValidatingPhone - -> CheckValidatingEmail
    CheckValidatingEmail: Start email validation if email from\nbasic info screen does not match email\nfrom registration session cookie and\nset cookie value to new number

    CheckValidatingEmail - -> ValidateInfoScreen: Save registration session cookie
    ValidateInfoScreen: phone code\nemail verification status

    ValidateInfoScreen - -> BasicInfoScreen: Basic info button

    ValidateInfoScreen - -> GetRegistrationSessionCookie: Register button

    GetRegistrationSessionCookie - -> BasicInfoScreen: Registration session expired

    GetRegistrationSessionCookie - -> CheckIfPhoneIsValidated: Load saved values from cookie

    CheckIfPhoneIsValidated - -> CheckSMSCode: phone not validated yet
    CheckSMSCode: From the validate info form
    CheckSMSCode - -> ValidateInfoScreen: No code or invalid code
    CheckSMSCode - -> BasicInfoScreen: Validation key expired
    CheckSMSCode - -> CheckIfValidatedEmailIsRequired: SMSCode is valid\nNumber is confirmed
    CheckIfValidatedEmailIsRequired: No clientid query param\nuser:validated:email scope requested\nrequirevalidatedemail queryparam set with non empty value
    CheckIfValidatedEmailIsRequired - -> CheckIfEmailIsValidated: Validated email required 
    CheckIfEmailIsValidated: Must be validated by\npressing the button in the email
    CheckIfEmailIsValidated -right- -> ValidateInfoScreen: Email not validated yet
    CheckIfValidatedEmailIsRequired - -> DeleteRegistrationSessionValues: Validated email not required
    CheckIfEmailIsValidated - -> DeleteRegistrationSessionValues: Email is validated
    DeleteRegistrationSessionValues - -> [*]: Set redirectparams value in registration session\nSave registration session cookie
}

RegisterScreen - -> UserIsLoggedIn
UserIsLoggedIn - -> [*]

@enduml

PlantUML version 1.2017.15(Mon Jul 03 18:45:34 CEST 2017)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Java Version: 1.8.0_131-8u131-b11-2ubuntu1.16.04.3-b11
Operating System: Linux
OS Version: 4.8.11-040811-generic
Default Encoding: UTF-8
Language: en
Country: US
--></g></svg>