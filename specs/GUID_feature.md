# IYO ID feature

## Description

A set of api endpoints will be made available under the /user api. These will allow
the generation and lookup of random strings representing a user identifier. Generation and
lookup of these identifiers is transparent to the user.

Scopes that give access to this functionality:

  - `user:admin` (Since we won't expose this in the UI for now I don't think this is required?)
  - `user:info` (anyone with an authorization for that user can request identifiers)

These identifiers can not be deleted: if an authorization is dropped but later reacquired,
it feels like expected behavior that old identifiers can still be recovered. This also
requires the ability to look them up in the database again.

These new identifiers should be able to be used as user identifiers in api calls.

## Identifier format

33 Random generated bytes, base64 encoded. This encoding should use the `urlencoding`
standard to ensure that there are no random `/` characters (important as we can use
these identifiers in urls). 33 bytes instead of 32 so there is no padding added by the
base64 encoding.

## Storage format

Info is stored in the database in a separate collection with the following fields
(naming subject to change):

  - `username`: The username of the person this identifier will representing
  - `azp`: The username or globalid of the party requesting this identifier.
    Only this party can use this identifier.
  - `iyoids`: A list with all of the generated identifiers for the above username and azp

## Endpoints

1. `POST /users/{username}/identifiers`
  - Generate a new identifier.
  - Use the client_id from the request as set by the middleware as the azp
  - Save the user identifier in the db and return only this id in the response body
  - There is a limit to the max amount of identifiers a user-azp relation can have - currently 25

2. `GET /users/{username}/identifiers`
  - List all generated identifiers for this party
  - Does not generate a new identifier itself

3. `GET /users/identifier/{identifier}`
  - Lookup the username behind an identifier;
  - Only if the client_id from the request matches the one saved in the db
  - If the id is not found, or the id does not belong to this azp, return `404` 
(not found for user - azp releation)


## Additional work

Because the user identifier middlewares need to be aware of the `client_id` to correctly
authorize or block calls with a `GUID` as identifier, the `client_id` would ideally already
be known when we try to dereference the identifier. This is currenlty not really possible
since it is taken from the authorization supplied or cookie. 

To solve this, the new middleware should set a context value with the `expected_client_id`,
which can then later be checked against the actual `client_id`

Also the functionality of the useridentifier middleware should be put in a separate function
so that it can be reused by the endpoints which expect a json format.

## Possbile issues:

- Client credentials jwt for users:

  The authorized party in said jwts is a random string, as it is the clientid from the
  user api key (as said this is randomly generated, unlike the clientid of an organization).
  If we use this authorized party in all cases (as we should), users will be able to
  request identifiers for themselves via the api (again, as they should), but this
  identifier will be linked to the user api key instead of the user. As a result:

  - Users with multiple api keys will need to remember which exact key the used
    if they want to verify that an identifier is indeed linked to them
  - If they delete the api key, they will forever loose the ability to verify this,
    as it is linked to the apikey.

  A possible solution for this would be to check if the authorization is provided by a
  user client credentials jwt or access token, and in this case take the username from the jwt. But this
  feels like building in code dependency and loss of separation again.

