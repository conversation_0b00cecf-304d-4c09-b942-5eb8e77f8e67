package organization

import (
	"git.gig.tech/gig-meneja/iam/db"
	"git.gig.tech/gig-meneja/iam/oauthservice"
	log "github.com/sirupsen/logrus"
)

// CreateRootOrganizationAPIkey adds API key to the root organization
func CreateRootOrganizationAPIkey(orgName, apiKey, callbackURL string) {
	c := oauthservice.NewOauth2Client(orgName, "root", callbackURL, true, "builtin:system")
	c.Secret = apiKey
	session := db.NewSession()
	defer session.Close()
	mgr := oauthservice.NewSessionManager(session)
	err := mgr.CreateClient(c)
	if db.IsDup(err) {
		log.Debug("Duplicate label")
	}
}
