package organization

//This file is auto-generated by go-raml
//Do not edit this file by hand since it will be overwritten during the next generation

import (
	"net/http"

	"github.com/gorilla/mux"
	"github.com/justinas/alice"
)

// OrganizationsInterface is interface for /organizations root endpoint
type OrganizationsInterface interface { // CreateNewOrganization is the handler for POST /organizations
	// Create a new organization. 1 user should be in the owners list. Validation is performed
	// to check if the securityScheme allows management on this user.
	CreateNewOrganization(http.ResponseWriter, *http.Request)
	// GetOrganization is the handler for GET /organizations/{globalid}
	// Get organization info
	GetOrganization(http.ResponseWriter, *http.Request)
	// CreateNewSubOrganization is the handler for POST /organizations/{globalid}
	// Create a new suborganization.
	CreateNewSubOrganization(http.ResponseWriter, *http.Request)
	// DeleteOrganization is the handler for DELETE /organizations/{globalid}
	// Removes an organization and all associated data.
	DeleteOrganization(http.ResponseWriter, *http.Request)
	// GetAPIKeyLabels is the handler for GET /organizations/{globalid}/apikeys
	// Get the list of active api keys. The secrets themselves are not included.
	GetAPIKeyLabels(http.ResponseWriter, *http.Request)
	// CreateNewAPIKey is the handler for POST /organizations/{globalid}/apikeys
	// Create a new API Key, a secret itself should not be provided, it will be generated
	// serverside.
	CreateNewAPIKey(http.ResponseWriter, *http.Request)
	// GetAPIKey is the handler for GET /organizations/{globalid}/apikeys/{label}
	GetAPIKey(http.ResponseWriter, *http.Request)
	// UpdateAPIKey is the handler for PUT /organizations/{globalid}/apikeys/{label}
	// Updates the label or other properties of a key.
	UpdateAPIKey(http.ResponseWriter, *http.Request)
	// DeleteAPIKey is the handler for DELETE /organizations/{globalid}/apikeys/{label}
	// Removes an API key
	DeleteAPIKey(http.ResponseWriter, *http.Request)
	// GetOrganizationTree is the handler for GET /organizations/{globalid}/tree
	GetOrganizationTree(http.ResponseWriter, *http.Request)
	// UpdateOrganizationMemberShip is the handler for PUT /organizations/{globalid}/members
	UpdateOrganizationMemberShip(http.ResponseWriter, *http.Request)
	// AddOrganizationMember is the handler for POST /organizations/{globalid}/members
	// Assign a member to organization.
	AddOrganizationMember(http.ResponseWriter, *http.Request)
	// RemoveOrganizationMember is the handler for DELETE /organizations/{globalid}/members/{username}
	// Remove a member from organization
	RemoveOrganizationMember(http.ResponseWriter, *http.Request)
	// AddOrganizationOwner is the handler for POST /organizations/{globalid}/owners
	// Invite a user to become owner of an organization.
	AddOrganizationOwner(http.ResponseWriter, *http.Request)
	RemoveOrganizationOwner(http.ResponseWriter, *http.Request)
	// GetInvitations is the handler for GET /organizations/{globalid}/invitations
	// Get the list of invitations for users to join this organization.
	GetInvitations(http.ResponseWriter, *http.Request)
	// RemovePendingInvitation is the handler for DELETE /organizations/{globalid}/invitations/user/{username}
	// Cancel a pending user invitation.
	RemovePendingInvitation(http.ResponseWriter, *http.Request)
	// RemovePendingInvitation is the handler for DELETE /organizations/{globalid}/invitations/organization/{username}
	// Cancel a pending organization invitation.
	RemoveOrgPendingInvitation(http.ResponseWriter, *http.Request)
	SetOrganizationLogo(http.ResponseWriter, *http.Request)
	// GetOrganizationLogo is the handler for GET /organizations/globalid/logo
	// Get the Logo from an organization
	GetOrganizationLogo(http.ResponseWriter, *http.Request)
	// DeleteOrganizationLogo is the handler for DELETE /organizations/globalid/logo
	// Removes the Logo from an organization
	DeleteOrganizationLogo(http.ResponseWriter, *http.Request)
	// Get2faValidityTime is the handler for GET /organizations/globalid/2fa/validity
	// Get the 2fa validity time for the organization, in seconds
	Get2faValidityTime(w http.ResponseWriter, r *http.Request)
	// Set2faValidityTime is the handler for PUT /organizations/globalid/2fa/validity
	// Sets the 2fa validity time for the organization, in seconds
	Set2faValidityTime(w http.ResponseWriter, r *http.Request)
	// SetOrgMember is the handler for POST /organizations/globalid/orgmembers
	// Sets an organization as a member of this one.
	SetOrgMember(w http.ResponseWriter, r *http.Request)
	// SetOrgOwner is the handler for POST /organizations/globalid/orgowners
	// Sets an organization as an owner of this one.
	SetOrgOwner(w http.ResponseWriter, r *http.Request)
	// DeleteOrgMember is the handler for Delete /organizations/globalid/orgmembers/globalid2
	// Removes an organization as a member of this one.
	DeleteOrgMember(w http.ResponseWriter, r *http.Request)
	// DeleteOrgOwner is the handler for Delete /organizations/globalid/orgowners/globalid2
	// Removes an organization as an owner of this one.
	DeleteOrgOwner(w http.ResponseWriter, r *http.Request)
	// UpdateOrganizationOrgMemberShip is the handler for Put /organizations/globalid/orgmembers
	// Updates an organizations membership as part of another organization
	UpdateOrganizationOrgMemberShip(w http.ResponseWriter, r *http.Request)
	// AddRequiredScope is the handler for POST /organizations/globalid/requiredscopes
	// Adds a required scope
	AddRequiredScope(w http.ResponseWriter, r *http.Request)
	// UpdateRequiredScope is the handler for PUT /organizations/globalid/requiredscopes/{requiredscope}
	// Updates a required scope
	UpdateRequiredScope(w http.ResponseWriter, r *http.Request)
	// DeleteRequiredScope is the handler for DELETE /organizations/globalid/requiredscopes/{requiredscope}
	// Deletes a required scope
	DeleteRequiredScope(w http.ResponseWriter, r *http.Request)
	// GetOrganizationUsers is the handler for GET /organizations/{globalid}/members
	// Get the list of all users in this organization
	GetOrganizationUsers(w http.ResponseWriter, r *http.Request)
	// GetDescription is the handler for GET /organizations/{globalid}/description/{langkey}
	// Get the description for this organization for this langKey
	GetDescription(w http.ResponseWriter, r *http.Request)
	// DeleteDescription is the handler for DELETE /organizations/{globalid}/description/{langkey}
	// Delete the description for this organization for this langKey
	DeleteDescription(w http.ResponseWriter, r *http.Request)
	// SetDescription is the handler for POST /organizations/{globalid}/description
	// Set the description for this organization for this langKey
	SetDescription(w http.ResponseWriter, r *http.Request)
	// UpdateDescription is the handler for PUT /organizations/{globalid}/description
	// Updates the description for this organization for this langKey
	UpdateDescription(w http.ResponseWriter, r *http.Request)
	// GetDescriptionWithFallback is the handler for GET /organizations/{globalid}/description/{langkey}/withfallback
	// Get the description for this organization for this langKey. If it doesn't exist, get the desription for the default langKey
	GetDescriptionWithFallback(w http.ResponseWriter, r *http.Request)
	// AcceptOrganizationInvite is the handler for POST /organizations/{globalid}/organizations/{invitingorg}/roles/{role}
	// Accept the organization invite for one of your organizations
	AcceptOrganizationInvite(w http.ResponseWriter, r *http.Request)
	// Rejectorganizationinvite is the handler for DELETE /organization/{globalid}/organizations/{invitingorg}/roles/{role}
	// Reject the organization invite for one of your organizations
	RejectOrganizationInvite(w http.ResponseWriter, r *http.Request)
	// AddIncludeSubOrgsOf is the handler for POST /organization/{globalid}/orgmembers/includesuborgs
	// Include the suborganizations of the given organization in the member/owner hierarchy of this organization
	AddIncludeSubOrgsOf(w http.ResponseWriter, r *http.Request)
	// RemoveIncludeSubOrgsOf is the handler for DELETE /organization/{globalid}/orgmembers/includesuborgs/{orgmember}
	// Removes the suborganizations of the given organization from the member/owner hierarchy of this organization
	RemoveIncludeSubOrgsOf(w http.ResponseWriter, r *http.Request)
	// UserIsMember is the handler for GET /organization/{globalid}/users/ismember/{username}
	// Checks if the user has membership rights on the organization
	UserIsMember(w http.ResponseWriter, r *http.Request)
	// GetUserGrants is the handler for GET /organization/{globalid}/grants/{user}
	// Lists all grants for a user
	GetUserGrants(w http.ResponseWriter, r *http.Request)
	// DeleteUserGrant is the handler for DELETE /organization/{globalid}/grants/{user}/{grant}
	// Removes a single named grant for the user
	DeleteUserGrant(w http.ResponseWriter, r *http.Request)
	// DeleteAllUserGrants is the handler for DELETE /organization/{globalid}/grants/{user}
	// Removes all grants for a user given by an organization
	DeleteAllUserGrants(w http.ResponseWriter, r *http.Request)
	// CreateUserGrant is the handler for POST /organization/{globalid}/grants
	// Gives a new grant to a user
	CreateUserGrant(w http.ResponseWriter, r *http.Request)
	// UpdateUserGrant is the handler for PUT /organization/{globalid}/grants
	// Changes an existing grant to a new one
	UpdateUserGrant(w http.ResponseWriter, r *http.Request)
	// ListUsersWithGrant is the handler for GET /organization/{globalid}/grants/havegrant/{grant}
	// Lists all users with a given grant
	ListUsersWithGrant(w http.ResponseWriter, r *http.Request)
	// List Users in all suborganizations  GET /organization/{globalid}/suborganizations/users
	GetSubOrganizationUsers(w http.ResponseWriter, r *http.Request)
	// List Organizations in all suborganizations GET /organization/{globalid}/suborganizations/memberships
	GetSubOrganizationMembrships(w http.ResponseWriter, r *http.Request)
}

// OrganizationsInterfaceRoutes is routing for /organizations root endpoint
func OrganizationsInterfaceRoutes(r *mux.Router, i OrganizationsInterface) {
	r.Handle("/organizations", alice.New(newOauth2oauth_2_0Middleware([]string{}).Handler, createAudit).Then(http.HandlerFunc(i.CreateNewOrganization))).Methods("POST")
	r.Handle("/organizations/{globalid}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:member", "organization:owner", "user:ownerof:organization"}).Handler, createAudit).Then(http.HandlerFunc(i.GetOrganization))).Methods("GET")
	r.Handle("/organizations/{globalid}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner", "user:ownerof:organization"}).Handler, createAudit).Then(http.HandlerFunc(i.CreateNewSubOrganization))).Methods("POST")
	r.Handle("/organizations/{globalid}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.DeleteOrganization))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/apikeys", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.GetAPIKeyLabels))).Methods("GET")
	r.Handle("/organizations/{globalid}/apikeys", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.CreateNewAPIKey))).Methods("POST")
	r.Handle("/organizations/{globalid}/apikeys/{label}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.GetAPIKey))).Methods("GET")
	r.Handle("/organizations/{globalid}/apikeys/{label}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.UpdateAPIKey))).Methods("PUT")
	r.Handle("/organizations/{globalid}/apikeys/{label}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.DeleteAPIKey))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/tree", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:member", "organization:owner", "user:ownerof:organization"}).Handler, createAudit).Then(http.HandlerFunc(i.GetOrganizationTree))).Methods("GET")
	r.Handle("/organizations/{globalid}/members", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.AddOrganizationMember))).Methods("POST")
	r.Handle("/organizations/{globalid}/members", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.UpdateOrganizationMemberShip))).Methods("PUT")
	r.Handle("/organizations/{globalid}/members/{username}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.RemoveOrganizationMember))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/owners", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.AddOrganizationOwner))).Methods("POST")
	r.Handle("/organizations/{globalid}/owners/{username}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.RemoveOrganizationOwner))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/invitations", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.GetInvitations))).Methods("GET")
	r.Handle("/organizations/{globalid}/invitations/user/{searchstring}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.RemovePendingInvitation))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/invitations/organization/{globalid2}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.RemoveOrgPendingInvitation))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/suborganizations", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.CreateNewSubOrganization))).Methods("POST")
	r.Handle("/organizations/{globalid}/tree", alice.New(newOauth2oauth_2_0Middleware([]string{}).Handler, createAudit).Then(http.HandlerFunc(i.GetOrganizationTree))).Methods("GET")
	r.Handle("/organizations/{globalid}/logo", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.SetOrganizationLogo))).Methods("PUT")
	r.Handle("/organizations/{globalid}/logo", http.HandlerFunc(i.GetOrganizationLogo)).Methods("GET")
	r.Handle("/organizations/{globalid}/logo", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.DeleteOrganizationLogo))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/2fa/validity", http.HandlerFunc(i.Get2faValidityTime)).Methods("GET")
	r.Handle("/organizations/{globalid}/2fa/validity", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.Set2faValidityTime))).Methods("PUT")
	r.Handle("/organizations/{globalid}/orgmembers", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner", "user:ownerof:organization"}).Handler, createAudit).Then(http.HandlerFunc(i.SetOrgMember))).Methods("POST")
	r.Handle("/organizations/{globalid}/orgowners", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner", "user:ownerof:organization"}).Handler, createAudit).Then(http.HandlerFunc(i.SetOrgOwner))).Methods("POST")
	r.Handle("/organizations/{globalid}/orgmembers/{globalid2}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner", "user:ownerof:organization"}).Handler, createAudit).Then(http.HandlerFunc(i.DeleteOrgMember))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/orgowners/{globalid2}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner", "user:ownerof:organization"}).Handler, createAudit).Then(http.HandlerFunc(i.DeleteOrgOwner))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/orgmembers", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.UpdateOrganizationOrgMemberShip))).Methods("PUT")
	r.Handle("/organizations/{globalid}/requiredscopes", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.AddRequiredScope))).Methods("POST")
	r.Handle("/organizations/{globalid}/requiredscopes/{requiredscope}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.UpdateRequiredScope))).Methods("PUT")
	r.Handle("/organizations/{globalid}/requiredscopes/{requiredscope}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.DeleteRequiredScope))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/users", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner", "organization:member"}).Handler, createAudit).Then(http.HandlerFunc(i.GetOrganizationUsers))).Methods("GET")
	r.Handle("/organizations/{globalid}/description/{langkey}", http.HandlerFunc(i.GetDescription)).Methods("GET")
	r.Handle("/organizations/{globalid}/description/{langkey}/withfallback", http.HandlerFunc(i.GetDescriptionWithFallback)).Methods("GET")
	r.Handle("/organizations/{globalid}/description/{langkey}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.DeleteDescription))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/description", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.SetDescription))).Methods("POST")
	r.Handle("/organizations/{globalid}/description", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.UpdateDescription))).Methods("PUT")
	r.Handle("/organizations/{globalid}/organizations/{invitingorg}/roles/{role}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.AcceptOrganizationInvite))).Methods("POST")
	r.Handle("/organizations/{globalid}/organizations/{invitingorg}/roles/{role}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.RejectOrganizationInvite))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/orgmembers/includesuborgs", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.AddIncludeSubOrgsOf))).Methods("POST")
	r.Handle("/organizations/{globalid}/orgmembers/includesuborgs/{orgmember}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.RemoveIncludeSubOrgsOf))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/users/ismember/{username}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.UserIsMember))).Methods("GET")
	r.Handle("/organizations/{globalid}/grants/{username}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.GetUserGrants))).Methods("GET")
	r.Handle("/organizations/{globalid}/grants/{username}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.DeleteAllUserGrants))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/grants/{username}/{grant}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.DeleteUserGrant))).Methods("DELETE")
	r.Handle("/organizations/{globalid}/grants", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.CreateUserGrant))).Methods("POST")
	r.Handle("/organizations/{globalid}/grants", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.UpdateUserGrant))).Methods("PUT")
	r.Handle("/organizations/{globalid}/grants/havegrant/{grant}", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.ListUsersWithGrant))).Methods("GET")
	r.Handle("/organizations/{globalid}/suborganizations/users", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.GetSubOrganizationUsers))).Methods("GET")
	r.Handle("/organizations/{globalid}/suborganizations/memberships", alice.New(newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler, createAudit).Then(http.HandlerFunc(i.GetSubOrganizationMembrships))).Methods("GET")
}
