package organization

import (
	"bytes"
	"context"
	"encoding/json"
	"io/ioutil"
	"net"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"time"

	"git.gig.tech/gig-meneja/iam/db/user"
	"git.gig.tech/gig-meneja/iam/globalconfig"
	"github.com/go-redis/redis/v8"
	gorillaContext "github.com/gorilla/context"
	log "github.com/sirupsen/logrus"
)

type AuditData struct {
	Method       string
	Path         string
	Query        string
	Username     string
	Data         string
	FullName     string
	Email        string
	ResponseTime float64
	StatusCode   int
	StatusText   string
	ResponseData string
	Header       string
}

func getRedisClient() redis.Client {
	addrPort := strings.Split(os.Getenv("MNJ_DYNAQUEUE_REDIS"), ":")
	if len(addrPort) < 2 {
		addrPort = []string{"localhost", "6379"}
	}
	addr := addrPort[0]
	port := addrPort[1]
	ips, err := net.LookupHost(addr)
	if err != nil {
		log.Info("Couldn't resolve ips from redis hostname")
		log.Debug(err.Error())
	}
	redisSentinelCluster, found := os.LookupEnv("MNJ_DYNAQUEUE_REDIS_SENTINEL_CLUSTER_NAME")
	redisClient := &redis.Client{}
	if found {
		log.Info("Connecting to redis sentinel")
		addrs := []string{}
		for _, ip := range ips {
			addrs = append(addrs, ip+":"+port)
		}
		redisClient = redis.NewFailoverClient(&redis.FailoverOptions{
			MasterName:    redisSentinelCluster,
			SentinelAddrs: addrs,
		})
		log.Debug(addrs)
		log.Debug(redisClient)
		log.Debug(ips)
	} else {
		redisClient = redis.NewClient(&redis.Options{
			Addr: addr + ":" + port,
		})
	}
	log.Debugf("Connected to redis on address %s", addr)
	return *redisClient
}

func createAudit(handler http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		config := globalconfig.NewManager()
		fullName := ""
		email := ""
		IamHostName, err := config.GetByKey("iamHostName")
		if err != nil {
			log.Debug("Error while reading host name from config", err.Error())
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		username := gorillaContext.Get(r, "authenticateduser").(string)
		if username == "" {
			username = "builtin:system"
		} else if username != "builtin:system" {
			userMgr := user.NewManager(r)
			userobj, err := userMgr.GetByName(username)
			if err != nil {
				log.Debugf("user %s not found", username)
				http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
				return
			}
			fullName = userobj.Firstname + " " + userobj.Lastname
			email = userobj.EmailAddresses[0].EmailAddress
		}
		body, err := ioutil.ReadAll(r.Body)
		if err != nil {
			log.Errorf("Error reading body: %v", err)
			http.Error(w, "can't read body", http.StatusBadRequest)
			return
		}
		r.Body = ioutil.NopCloser(bytes.NewBuffer(body))
		c := httptest.NewRecorder()
		startTime := time.Now()
		handler.ServeHTTP(c, r)
		respTime := time.Since(startTime).Seconds()
		for key, val := range c.HeaderMap {
			w.Header()[key] = val
		}
		headers, err := json.Marshal(c.Result().Header)
		if err != nil {
			log.Info("Failed to marshal header into JSON")
			return
		}
		w.WriteHeader(c.Code)
		c.Body.WriteTo(w)
		audit := &AuditData{r.Method, IamHostName.Value + r.URL.Path, r.URL.RawQuery, username, string(body), fullName, email, respTime, c.Code, c.Result().Status, c.Body.String(), string(headers)}
		auditJson, err := json.Marshal(audit)
		if err != nil {
			log.Info("Failed to marshal audits into JSON")
			return
		}
		redisClient := getRedisClient()
		redisClient.LPush(context.Background(), IamHostName.Value+"-iam-audits", string(auditJson))
	})
}
