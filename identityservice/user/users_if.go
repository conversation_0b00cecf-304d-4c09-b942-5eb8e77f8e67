package user

import (
	"net/http"

	"git.gig.tech/gig-meneja/iam/siteservice/middleware"
	"github.com/gorilla/mux"
	"github.com/justinas/alice"
)

// UsersInterface is interface for /users root endpoint
type UsersInterface interface { // Post is the handler for POST /users
	// Create a new user
	Post(http.ResponseWriter, *http.Request)
	//DELTE current user
	DeleteCurrentUser(http.ResponseWriter, *http.Request)
	// UpdateName is the handler for PUT / users/{username}/name
	UpdateName(http.ResponseWriter, *http.Request)
	// UpdatePassword is the handler for PUT /users/{username}/password
	UpdatePassword(http.ResponseWriter, *http.Request)
	// GetUserPhoneNumbers is the handler for GET /users/{username}/phonenumbers
	GetUserPhoneNumbers(http.ResponseWriter, *http.Request)
	// RegisterNewPhonenumber is the handler for POST /users/{username}/phonenumbers
	// Register a new phonenumber
	RegisterNewPhonenumber(http.ResponseWriter, *http.Request)
	// ValidatePhoneNumber is the handler for POST /users/{username}/phonenumbers/{label}/validate
	// Send sms verification to phone number
	ValidatePhoneNumber(http.ResponseWriter, *http.Request)
	// VerifyPhoneNumber is the handler for PUT /users/{username}/phonenumbers/{label}/validate
	// Verifies a phone number
	VerifyPhoneNumber(http.ResponseWriter, *http.Request)
	// GetUserPhonenumberByLabel is the handler for GET /users/{username}/phonenumbers/{label}
	GetUserPhonenumberByLabel(http.ResponseWriter, *http.Request)
	// UpdatePhonenumber is the handler for PUT /users/{username}/phonenumbers/{label}
	// Update the label and/or value of an existing phonenumber.
	UpdatePhonenumber(http.ResponseWriter, *http.Request)
	// DeletePhonenumber is the handler for DELETE /users/{username}/phonenumbers/{label}
	// Removes a phonenumber
	DeletePhonenumber(http.ResponseWriter, *http.Request)
	// GetNotifications is the handler for GET /users/{username}/notifications
	// Get the list of notifications, these are pending invitations or approvals
	GetNotifications(http.ResponseWriter, *http.Request)
	// GetUser is the handler for GET /users/{username}
	GetUser(http.ResponseWriter, *http.Request)
	// RegisterNewEmailAddress is the handler for POST /users/{username}/emailaddresses
	// Register a new email address
	RegisterNewEmailAddress(http.ResponseWriter, *http.Request)
	// UpdateName is the handler for PUT / users/{username}/name
	UpdateEmailTwoFA(http.ResponseWriter, *http.Request)
	// UpdateEmailAddress is the handler for PUT /users/{username}/emailaddresses/{label}
	// Updates the label and/or value of an email address
	UpdateEmailAddress(http.ResponseWriter, *http.Request)
	// DeleteEmailAddress is the handler for DELETE /users/{username}/emailaddresses/{label}
	// Removes an email address
	DeleteEmailAddress(http.ResponseWriter, *http.Request)
	ValidateEmailAddress(http.ResponseWriter, *http.Request)
	VerifyEmailAddress(http.ResponseWriter, *http.Request)
	ListEmailAddresses(http.ResponseWriter, *http.Request)
	// GetUserInformation is the handler for GET /users/{username}/info
	GetUserInformation(http.ResponseWriter, *http.Request)
	// GetAllAuthorizations is the handler for GET /users/{username}/authorizations
	// Get the list of authorizations.
	GetAllAuthorizations(http.ResponseWriter, *http.Request)
	// GetAuthorization is the handler for GET /users/{username}/authorizations/{grantedTo}
	// Get the authorization for a specific organization.
	GetAuthorization(http.ResponseWriter, *http.Request)
	// UpdateAuthorization is the handler for PUT /users/{username}/authorizations/{grantedTo}
	// Modify which information an organization is able to see.
	UpdateAuthorization(http.ResponseWriter, *http.Request)
	// DeleteAuthorization is the handler for DELETE /users/{username}/authorizations/{grantedTo}
	// Remove the authorization for an organization, the granted organization will no longer
	// have access the user's information.
	DeleteAuthorization(http.ResponseWriter, *http.Request)
	// AddAPIKey Add an API Key
	AddAPIKey(http.ResponseWriter, *http.Request)
	GetAPIKey(http.ResponseWriter, *http.Request)
	UpdateAPIKey(http.ResponseWriter, *http.Request)
	DeleteAPIKey(http.ResponseWriter, *http.Request)
	ListAPIKeys(http.ResponseWriter, *http.Request)
	// AddPublicKey Add a public key
	AddPublicKey(http.ResponseWriter, *http.Request)
	// GetPublicKey Get the public key associated with a label
	GetPublicKey(http.ResponseWriter, *http.Request)
	// UpdatePublicKey Updates the label and or key of an existing public key
	UpdatePublicKey(http.ResponseWriter, *http.Request)
	// DeletePublicKey Deletes a public key
	DeletePublicKey(http.ResponseWriter, *http.Request)
	// ListPublicKeys Lists all public keys
	ListPublicKeys(http.ResponseWriter, *http.Request)
	// GetKeyStore is the handler for GET /users/{username}/keystore
	// Returns all the publickeys written to the user by an organizaton
	GetKeyStore(http.ResponseWriter, *http.Request)
	// GetKeyStoreKey is the handler for GET /users/{username}/keystore{label}
	// Returns all specific publickey written to the user by an organizaton
	GetKeyStoreKey(http.ResponseWriter, *http.Request)
	// SaveKeyStoreKey is the handler for POST /users/{username}/keystore
	// Returns all the publickeys written to the user by an organizaton
	SaveKeyStoreKey(http.ResponseWriter, *http.Request)
	// GetTwoFAMethods is the handler for GET /users/{username}/twofamethods
	// Get the possible two factor authentication methods
	GetTwoFAMethods(http.ResponseWriter, *http.Request)
	// GetTOTPSecret is the handler for GET /users/{username}/totp
	GetTOTPSecret(http.ResponseWriter, *http.Request)
	// SetupTOTP is the handler for POST /users/{username}/totp
	SetupTOTP(http.ResponseWriter, *http.Request)
	// RemoveTOTP is the handler for DELETE /users/{username}/totp
	RemoveTOTP(http.ResponseWriter, *http.Request)
	// LeaveOrganization is the handler for DELETE /users/{username}/organizations/{globalid}/leave
	LeaveOrganization(http.ResponseWriter, *http.Request)
	ListIyoIDs(http.ResponseWriter, *http.Request)
	// GenerateIyoID is the handler for POST /users/{username}/identifiers
	// Generate a new iyo id for a user and authorized party
	GenerateIyoID(http.ResponseWriter, *http.Request)
	// LookupIyoID is the handler for GET /users/{username}/identifiers/{identifier}
	// Lookup the username behind an iyo id
	LookupIyoID(http.ResponseWriter, *http.Request)
	// GetOIDCProviders is the handler for GET /users/{username}/oidc-providers
	// Get all OIDC providers linked to the user
	GetOIDCProviders(http.ResponseWriter, *http.Request)
	// RevokeOIDCProvider is the handler for DELETE /users/{username}/oidc-providers/{provider}
	// Revoke access to an OIDC provider
	RevokeOIDCProvider(http.ResponseWriter, *http.Request)
}

// UsersInterfaceRoutes is routing for /users root endpoint
func UsersInterfaceRoutes(r *mux.Router, i UsersInterface) {
	r.Handle("/users", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"organization:owner"}).Handler).Then(http.HandlerFunc(i.Post))).Methods("POST")
	r.Handle("/users/{username}/name", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.UpdateName))).Methods("PUT")
	r.Handle("/users/{username}/password", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.UpdatePassword))).Methods("PUT")
	r.Handle("/users/{username}/phonenumbers", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.GetUserPhoneNumbers))).Methods("GET")
	r.Handle("/users/{username}/phonenumbers", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.RegisterNewPhonenumber))).Methods("POST")
	r.Handle("/users/{username}/phonenumbers/{label}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin", "user:phone:{label}", "user:phone:{label}:write"}).Handler).Then(http.HandlerFunc(i.GetUserPhonenumberByLabel))).Methods("GET")
	r.Handle("/users/{username}/phonenumbers/{label}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin", "user:phone:{label}:write"}).Handler).Then(http.HandlerFunc(i.UpdatePhonenumber))).Methods("PUT")
	r.Handle("/users/{username}/phonenumbers/{label}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin", "user:phone:{label}:write"}).Handler).Then(http.HandlerFunc(i.DeletePhonenumber))).Methods("DELETE")
	r.Handle("/users/{username}/phonenumbers/{label}/validate", alice.New(middleware.RateLimit(middleware.DefaultRateLimitPeriod, middleware.DefaultRateLimit).Handler, NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.ValidatePhoneNumber))).Methods("POST")
	r.Handle("/users/{username}/phonenumbers/{label}/validate", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.VerifyPhoneNumber))).Methods("PUT")
	r.Handle("/users/{username}/notifications", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.GetNotifications))).Methods("GET")
	r.Handle("/users/{username}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.GetUser))).Methods("GET")
	r.Handle("/users/{username}/apikeys", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.ListAPIKeys))).Methods("GET")
	r.Handle("/users/{username}/apikeys", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.AddAPIKey))).Methods("POST")
	r.Handle("/users/{username}/apikeys/{label}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.GetAPIKey))).Methods("GET")
	r.Handle("/users/{username}/apikeys/{label}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.UpdateAPIKey))).Methods("PUT")
	r.Handle("/users/{username}/apikeys/{label}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.DeleteAPIKey))).Methods("DELETE")
	r.Handle("/users/{username}/publickeys", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.ListPublicKeys))).Methods("GET")
	r.Handle("/users/{username}/publickeys", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.AddPublicKey))).Methods("POST")
	r.Handle("/users/{username}/publickeys/{label}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.GetPublicKey))).Methods("GET")
	r.Handle("/users/{username}/publickeys/{label}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.UpdatePublicKey))).Methods("PUT")
	r.Handle("/users/{username}/publickeys/{label}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.DeletePublicKey))).Methods("DELETE")
	r.Handle("/users/{username}/keystore", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:keystore"}).Handler).Then(http.HandlerFunc(i.GetKeyStore))).Methods("GET")
	r.Handle("/users/{username}/keystore/{label}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:keystore"}).Handler).Then(http.HandlerFunc(i.GetKeyStoreKey))).Methods("GET")
	r.Handle("/users/{username}/keystore", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:keystore"}).Handler).Then(http.HandlerFunc(i.SaveKeyStoreKey))).Methods("POST")
	r.Handle("/users/{username}/emailaddresses", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.ListEmailAddresses))).Methods("GET")
	r.Handle("/users/{username}/emailaddresses", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.RegisterNewEmailAddress))).Methods("POST")
	r.Handle("/users/{username}/emailtwofa", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.UpdateEmailTwoFA))).Methods("PUT")
	r.Handle("/users/{username}/emailaddresses/{label}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.UpdateEmailAddress))).Methods("PUT")
	r.Handle("/users/{username}/emailaddresses/{label}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.DeleteEmailAddress))).Methods("DELETE")
	r.Handle("/users/{username}/emailaddresses/{label}/validate", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.ValidateEmailAddress))).Methods("POST")
	r.Handle("/users/{username}/emailaddresses/{label}/validate", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.VerifyEmailAddress))).Methods("PUT")
	r.Handle("/users/{username}/info", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:info", "user:admin"}).Handler).Then(http.HandlerFunc(i.GetUserInformation))).Methods("GET")
	r.Handle("/users/{username}/info/yaml", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:info", "user:admin"}).Handler).Then(http.HandlerFunc(i.GetUserInformation))).Methods("GET")
	r.Handle("/me", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:info", "user:admin"}).Handler).Then(http.HandlerFunc(i.GetUserInformation))).Methods("GET")
	r.Handle("/me", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.DeleteCurrentUser))).Methods("DELETE")
	r.Handle("/users/{username}/authorizations", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.GetAllAuthorizations))).Methods("GET")
	r.Handle("/users/{username}/authorizations/{grantedTo}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.GetAuthorization))).Methods("GET")
	r.Handle("/users/{username}/authorizations/{grantedTo}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.UpdateAuthorization))).Methods("PUT")
	r.Handle("/users/{username}/authorizations/{grantedTo}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.DeleteAuthorization))).Methods("DELETE")
	r.Handle("/users/{username}/twofamethods", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.GetTwoFAMethods))).Methods("GET")
	r.Handle("/users/{username}/totp", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.GetTOTPSecret))).Methods("GET")
	r.Handle("/users/{username}/totp", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.SetupTOTP))).Methods("POST")
	r.Handle("/users/{username}/totp", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.RemoveTOTP))).Methods("DELETE")
	r.Handle("/users/{username}/organizations/{globalid}/leave", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.LeaveOrganization))).Methods("DELETE")
	r.Handle("/users/{username}/identifiers", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin", "user:info"}).Handler).Then(http.HandlerFunc(i.ListIyoIDs))).Methods("GET")
	r.Handle("/users/{username}/identifiers", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin", "user:info"}).Handler).Then(http.HandlerFunc(i.GenerateIyoID))).Methods("POST")
	r.Handle("/users/identifiers/{identifier}", alice.New(newClientIDMiddleware([]string{}).Handler).Then(http.HandlerFunc(i.LookupIyoID))).Methods("GET")
	r.Handle("/users/{username}/oidc-providers", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.GetOIDCProviders))).Methods("GET")
	r.Handle("/users/{username}/oidc-providers/{provider}", alice.New(NewUserIdentifierMiddleware().Handler, newOauth2oauth_2_0Middleware([]string{"user:admin"}).Handler).Then(http.HandlerFunc(i.RevokeOIDCProvider))).Methods("DELETE")
}
