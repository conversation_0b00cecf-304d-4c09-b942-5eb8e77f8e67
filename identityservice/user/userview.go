package user

import "git.gig.tech/gig-meneja/iam/db/user"

type Userview struct {
	EmailAddresses          []user.EmailAddress `json:"emailaddresses"`
	ValidatedEmailAddresses []user.EmailAddress `json:"validatedemailaddresses"`
	Organizations           OrganizationsInfo   `json:"organizations"`
	Phonenumbers            []user.Phonenumber  `json:"phonenumbers"`
	ValidatedPhonenumbers   []user.Phonenumber  `json:"validatedphonenumbers"`
	PublicKeys              []user.PublicKey    `json:"publicKeys"`
	Username                string              `json:"username"`
	Firstname               string              `json:"firstname"`
	Lastname                string              `json:"lastname"`
	OwnerOf                 user.OwnerOf        `json:"ownerof"`
}
