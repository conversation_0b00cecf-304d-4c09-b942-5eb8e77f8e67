module git.gig.tech/gig-meneja/iam

go 1.22

require (
	github.com/dgrijalva/jwt-go v2.6.1-0.20160504172548-40bd0f3b4891+incompatible
	github.com/go-gomail/gomail v0.0.0-20160411212932-81ebce5c23df
	github.com/go-redis/redis/v8 v8.11.5
	github.com/gorilla/context v0.0.0-20150820051245-1c83b3eabd45
	github.com/gorilla/csrf v1.7.1
	github.com/gorilla/handlers v1.3.0
	github.com/gorilla/mux v0.0.0-20151231161908-26a6070f8499
	github.com/gorilla/sessions v0.0.0-20151002135952-f7261893ca3e
	github.com/hgfischer/go-otp v0.0.0-20150321164713-38f86955cc64
	github.com/justinas/alice v0.0.0-20160301085426-29a850317649
	github.com/sirupsen/logrus v0.8.7
	github.com/stretchr/testify v1.5.1
	github.com/ulule/limiter v2.1.0+incompatible
	github.com/urfave/cli v1.11.1
	gopkg.in/mgo.v2 v2.0.0-20150902133524-22287bab4379
	gopkg.in/validator.v2 v2.0.0-20160201165114-3e4f037f12a1
	gopkg.in/yaml.v2 v2.4.0
)

require (
	github.com/cespare/xxhash/v2 v2.1.2 // indirect
	github.com/davecgh/go-spew v1.1.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/gorilla/securecookie v1.1.1 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	golang.org/x/time v0.8.0 // indirect
	gopkg.in/alexcesaro/quotedprintable.v3 v3.0.0-20150716171945-2caba252f4dc // indirect
	gopkg.in/check.v1 v1.0.0-20201130134442-10cb98267c6c // indirect
	gopkg.in/gomail.v2 v2.0.0-20160411212932-81ebce5c23df // indirect
)
