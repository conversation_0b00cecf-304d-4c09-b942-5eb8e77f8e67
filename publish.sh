#!/usr/bin/env bash
set -e

VERSION="$(git describe)"

echo "Building version $VERSION"

docker build -t iambuilder .
docker run --rm -v "$PWD":/go/src/git.gig.tech/gig-meneja/iam --entrypoint sh iambuilder -c "go generate && go build -ldflags '-s -X main.version=$VERSION' -v -o dist/identityserver"
docker build -t iam/identityserver:"$VERSION" -f DockerfileMinimal .

docker push iam/identityserver:"$VERSION"
