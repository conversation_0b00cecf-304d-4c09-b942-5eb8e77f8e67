events { }

http {
    server {
        listen 443 ssl;
        ssl_certificate iam-dev.gig.tech.local.cert;
        ssl_certificate_key iam-dev.gig.tech.local.key;
        server_name iam-dev.gig.tech.local;
        proxy_set_header Host $host;
        ignore_invalid_headers off;
        client_max_body_size 0;
        proxy_buffering off;        

        location / {
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 300;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            chunked_transfer_encoding off;
            proxy_pass http://identityserver:8080;
        }
    }
}