FROM golang:1.22

ARG GOBINDATAVERSION=a0ff2567cfb70903282db057e799fd826784d41d
ARG TOKEN

RUN go install github.com/go-bindata/go-bindata/...@latest

ARG GORAMLVERSION=a8cc1c065538cfc97ffc077d3a185f653ad406e6

RUN git clone https://gitlab-ci-token:$<EMAIL>/gig-meneja/forks/go-raml.git $GOPATH/src/github.com/Jumpscale/go-raml
WORKDIR $GOPATH/src/github.com/Jumpscale/go-raml
RUN git checkout $GORAMLVERSION
RUN ./build.sh

ENV CGO_ENABLED 0
RUN mkdir -p $GOPATH/src/git.gig.tech/gig-meneja/iam
COPY . $GOPATH/src/git.gig.tech/gig-meneja/iam/
WORKDIR $GOPATH/src/git.gig.tech/gig-meneja/iam

RUN go generate && go build && go test -v ./...

RUN mkdir -p /var/iam && cp iam /var/iam/ && cp -r devcert /var/iam

FROM ghub.gig.tech/gig-meneja/iam_ui/iam-ui-base:latest

FROM alpine:3

RUN addgroup -g 1000 iam && adduser -u 1000 -G iam -h /home/<USER>
USER iam
COPY --from=0 /var/iam /home/<USER>/server
COPY --from=1 /var/iam /home/<USER>/server/website
WORKDIR /home/<USER>/server
EXPOSE 8080

ENTRYPOINT ["./iam"]
