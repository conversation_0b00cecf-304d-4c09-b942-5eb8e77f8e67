# Summary

* [Introduction](README.md)
* Oauth2
   * [Oauth2 Flows](oauth2/oauth2.md)
       * [Customize the user experience](oauth2/CustomizeAuthorizationCodeFlow.md)
   * [Scope Concept](oauth2/scopes.md)
   * [Available Scopes](oauth2/availableScopes.md)
   * [JWT Support](oauth2/jwt.md)
   * [Suborganization globalid composition](oauth2/suborganizations.md)
* Organizations
    * [Organization ownership](organizations/organizationownership.md)
* [Securing an external api](externalapisecurity/externalapisecurity.md)
* [Staging environment](staging.md)
