language: go

# Workaround as sugested in https://github.com/travis-ci/travis-ci/issues/8836
sudo: required
addons:
  firefox: latest
  chrome: stable

install:
  - go get -u github.com/jteeuwen/go-bindata/...
  - go get -u -d github.com/Jumpscale/go-raml
  - npm install
  - pip install --user autopep8  

go:
  - 1.7
  - master

before_script:
  - export DISPLAY=:99.0
  - sh -e /etc/init.d/xvfb start
  # Some time for xvfb to start
  - sleep 3

script:
# Use last go raml version which still supports go 1.7
  - cd $GOPATH/src/github.com/Jumpscale/go-raml && git checkout 54440d5ac34a75d8991c7c0aee05b11adaefda3c && ./build.sh && cd -
  - go generate
  - go test -v ./...
  - npm test
