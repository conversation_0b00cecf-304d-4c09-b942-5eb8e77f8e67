FROM alpine/openssl

WORKDIR /root
RUN openssl req -new -newkey rsa:4096 -days 365 -nodes -x509 \
    -subj "/C=BE/ST=Flanders/L=Lochristi/O=Engineering/CN=iam-dev.gig.tech.local" \
    -keyout iam-dev.gig.tech.local.key  -out iam-dev.gig.tech.local.cert

FROM nginx
COPY nginx.conf /etc/nginx/nginx.conf
COPY --from=0 /root/iam-dev.gig.tech.local.key /etc/nginx/
COPY --from=0 /root/iam-dev.gig.tech.local.cert /etc/nginx/
RUN mkdir -p /var/certs && cp /etc/nginx/iam-dev.gig.tech.local.cert /var/certs
EXPOSE 443
