package user

import (
	"strings"
)

// Authorization defines what userinformation is authorized to be seen by an organization
// For an explanation about scopes and scopemapping, see https://git.gig.tech/gig-meneja/iam/blob/master/docs/oauth2/scopes.md
type Authorization struct {
	EmailAddresses          []AuthorizationMap `json:"emailaddresses,omitempty"`
	ValidatedEmailAddresses []AuthorizationMap `json:"validatedemailaddresses,omitempty"`
	GrantedTo               string             `json:"grantedTo"`
	Organizations           []string           `json:"organizations"`
	OwnedOrganizations      []string           `json:"ownedorganizations"`
	Phonenumbers            []AuthorizationMap `json:"phonenumbers,omitempty"`
	ValidatedPhonenumbers   []AuthorizationMap `json:"validatedphonenumbers,omitempty"`
	PublicKeys              []AuthorizationMap `json:"publicKeys,omitempty"`
	Username                string             `json:"username"`
	Name                    bool               `json:"name"`
	OwnerOf                 OwnerOf            `json:"ownerof,omitempty"`
	KeyStore                bool               `json:"keystore,omitempty"`
}

// AuthorizationMap structure
type AuthorizationMap struct {
	RequestedLabel string `json:"requestedlabel"`
	RealLabel      string `json:"reallabel"`
	Scope          string `json:"scope" bson:"scope,omitempty"` // "write" or nothing (for now)
}

// OwnerOf structure
type OwnerOf struct {
	EmailAddresses []string `json:"emailaddresses"`
}

//FilterAuthorizedScopes filters the requested scopes to the ones this Authorization covers
func (authorization Authorization) FilterAuthorizedScopes(requestedscopes []string) (authorizedScopes []string) {
	authorizedScopes = make([]string, 0, len(requestedscopes))
	for _, rawscope := range requestedscopes {
		scope := strings.TrimSpace(rawscope)
		if scope == "user:name" && authorization.Name {
			authorizedScopes = append(authorizedScopes, scope)
		}
		if strings.HasPrefix(scope, "user:memberof:") {
			requestedOrganizationId := strings.TrimPrefix(scope, "user:memberof:")
			if authorization.ContainsOrganization(requestedOrganizationId) {
				authorizedScopes = append(authorizedScopes, scope)
			}
		}
		if strings.HasPrefix(scope, "user:ownerof:organization:") {
			requestedOrganizationId := strings.TrimPrefix(scope, "user:ownerof:organization:")
			if authorization.ContainsOwnedOrganization(requestedOrganizationId) {
				authorizedScopes = append(authorizedScopes, scope)
			}
		}
		if strings.HasPrefix(scope, "user:organizations:") {
			if strings.TrimPrefix(scope, "user:organizations:") != "" {
				authorizedScopes = append(authorizedScopes, scope)
			}
		}
		if strings.HasPrefix(scope, "user:email") && LabelledPropertyIsAuthorized(scope, "user:email", authorization.EmailAddresses) {
			authorizedScopes = append(authorizedScopes, scope)
		}
		if strings.HasPrefix(scope, "user:phone") && LabelledPropertyIsAuthorized(scope, "user:phone", authorization.Phonenumbers) {
			authorizedScopes = append(authorizedScopes, scope)
		}
		if strings.HasPrefix(scope, "user:validated:email") && LabelledPropertyIsAuthorized(scope, "user:validated:email", authorization.ValidatedEmailAddresses) {
			authorizedScopes = append(authorizedScopes, scope)
		}
		if strings.HasPrefix(scope, "user:validated:phone") && LabelledPropertyIsAuthorized(scope, "user:validated:phone", authorization.ValidatedPhonenumbers) {
			authorizedScopes = append(authorizedScopes, scope)
		}
		if strings.HasPrefix(scope, "user:publickey") && LabelledPropertyIsAuthorized(scope, "user:publickey", authorization.PublicKeys) {
			authorizedScopes = append(authorizedScopes, scope)
		}
		if strings.HasPrefix(scope, "user:ownerof:email") && OwnerOfIsAuthorized(scope, "user:ownerof:email", authorization.OwnerOf.EmailAddresses) {
			authorizedScopes = append(authorizedScopes, scope)
		}
		if scope == "user:keystore" && authorization.KeyStore {
			authorizedScopes = append(authorizedScopes, scope)
		}

		// oidc scopes
		if scope == "openid" {
			authorizedScopes = append(authorizedScopes, scope)
		}
	}

	return
}

// ContainsOrganization function
func (authorization Authorization) ContainsOrganization(globalid string) bool {

	return contains(globalid, authorization.Organizations)
}

// ContainsOwnedOrganization function
func (authorization Authorization) ContainsOwnedOrganization(globalid string) bool {

	return contains(globalid, authorization.OwnedOrganizations)
}

func contains(globalid string, orgs []string) bool {
	for _, orgid := range orgs {
		if orgid == globalid {
			return true
		}
	}
	return false
}

// LabelledPropertyIsAuthorized checks if a labelled property is authorized
func LabelledPropertyIsAuthorized(scope string, scopePrefix string, authorizedLabels []AuthorizationMap) (authorized bool) {
	if authorizedLabels == nil {
		return
	}
	if scope == scopePrefix {
		authorized = len(authorizedLabels) > 0
		return
	}
	if strings.HasPrefix(scope, scopePrefix+":") {
		split := strings.Split(strings.TrimPrefix(scope, scopePrefix+":"), ":")
		requestedLabel := split[0]
		requestedScope := split[len(split)-1]
		if requestedLabel == requestedScope {
			requestedScope = ""
		}
		for _, authorizationmap := range authorizedLabels {
			if (authorizationmap.RequestedLabel == requestedLabel ||
				authorizationmap.RequestedLabel == "main" && requestedLabel == "") &&
				(authorizationmap.Scope == requestedScope || requestedScope == "") {
				authorized = true
				return
			}
		}
	}
	return
}

// Merge merges 2 authorizations.
func (authorization *Authorization) Merge(a *Authorization) {
	// Merge all but the grantedTo and username fields, those are already the same
	// Do some sanity checking anyway
	if authorization.Username != a.Username || authorization.GrantedTo != a.GrantedTo {
		return
	}

	// Bool fields are merged on a once given is alwasy given basis
	authorization.Name = authorization.Name || a.Name
	authorization.KeyStore = authorization.KeyStore || a.KeyStore

	// Authorized organizations can simply be expanded
	authorization.Organizations = append(authorization.Organizations, a.Organizations...)

	// Ownerof is an abstraction over a []string, can also be expanded
	authorization.OwnerOf.EmailAddresses = append(authorization.OwnerOf.EmailAddresses, a.OwnerOf.EmailAddresses...)

	authorization.EmailAddresses = mergeAuthorizationMaps(authorization.EmailAddresses, a.EmailAddresses)
	authorization.Phonenumbers = mergeAuthorizationMaps(authorization.Phonenumbers, a.Phonenumbers)
	authorization.PublicKeys = mergeAuthorizationMaps(authorization.PublicKeys, a.PublicKeys)
	authorization.ValidatedEmailAddresses = mergeAuthorizationMaps(authorization.ValidatedEmailAddresses, a.ValidatedEmailAddresses)
	authorization.ValidatedPhonenumbers = mergeAuthorizationMaps(authorization.ValidatedPhonenumbers, a.ValidatedPhonenumbers)
}

// mergeAuthorizationMaps merges 2 authorizationmaps, overwriting requestedlabels
// with those from the authorizationmap provided by the new Authorization
func mergeAuthorizationMaps(newAuths []AuthorizationMap, oldAuths []AuthorizationMap) []AuthorizationMap {
OLDAUTHS:
	for _, oldAuth := range oldAuths {
		// check all new auths to make sure it isn't overwritten
		for _, newAuth := range newAuths {
			// overwritten, continue with the next oldAuth
			if oldAuth.RequestedLabel == newAuth.RequestedLabel {
				continue OLDAUTHS
			}
		}
		// All new auths checked and no match, so lets append it
		newAuths = append(newAuths, oldAuth)
	}

	return newAuths
}

// OwnerOfIsAuthorized function
func OwnerOfIsAuthorized(scope string, scopePrefix string, authorizedOwnerOfs []string) bool {
	if authorizedOwnerOfs == nil {
		return false
	}
	for _, authorizedOwnerOf := range authorizedOwnerOfs {
		requestedOwnerOf := strings.TrimPrefix(scope, scopePrefix+":")
		if authorizedOwnerOf == requestedOwnerOf {
			return true
		}
	}
	return false
}
