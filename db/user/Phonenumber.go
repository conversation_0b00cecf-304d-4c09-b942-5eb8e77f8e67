package user

import (
	"regexp"

	"gopkg.in/validator.v2"
)

//Phonenumber defines a phonenumber and has functions for validation
type Phonenumber struct {
	Label       string `json:"label" validate:"regexp=^[a-zA-Z\\d\\-_\\s]+$"`
	Phonenumber string `json:"phonenumber" validate:"regexp=^\\+[0-9 \\-]+$"`
}

//Validate checks if a phone number is in a valid format
func (p Phonenumber) Validate() bool {
	return validator.Validate(p) == nil &&
		regexp.MustCompile(`^[a-zA-Z\d\-_\s]{2,50}$`).MatchString(p.Label) &&
		regexp.MustCompile(`^\+[0-9 \-]{6,55}$`).MatchString(p.Phonenumber)
}
