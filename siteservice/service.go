package siteservice

import (
	"fmt"
	"html/template"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"git.gig.tech/gig-meneja/iam/communication"
	"git.gig.tech/gig-meneja/iam/db"
	"git.gig.tech/gig-meneja/iam/oauthservice"
	"git.gig.tech/gig-meneja/iam/siteservice/middleware"
	"git.gig.tech/gig-meneja/iam/validation"
	"github.com/gorilla/csrf"
	"github.com/gorilla/mux"
	"github.com/gorilla/sessions"
	"github.com/justinas/alice"

	"encoding/json"

	"sync"
	"time"

	"golang.org/x/time/rate"

	"git.gig.tech/gig-meneja/iam/credentials/totp"
	"git.gig.tech/gig-meneja/iam/identityservice"
	log "github.com/sirupsen/logrus"
)

// Service is the identityserver http service
type Service struct {
	Sessions                      map[SessionType]*sessions.CookieStore
	smsService                    communication.SMSService
	phonenumberValidationService  *validation.IYOPhonenumberValidationService
	EmailService                  communication.EmailService
	emailaddressValidationService *validation.IYOEmailAddressValidationService
	version                       string
	testEnv                       bool
	identityService               *identityservice.Service
	staticPath                    string
	indexPath                     string
}

// NewService creates and initializes a Service
func NewService(cookieSecret string, smsService communication.SMSService, emailService communication.EmailService,
	identityservice *identityservice.Service, version string, testEnv bool) (service *Service) {
	service = &Service{smsService: smsService}

	p := &validation.IYOPhonenumberValidationService{SMSService: smsService}
	service.phonenumberValidationService = p
	e := &validation.IYOEmailAddressValidationService{EmailService: emailService}
	service.emailaddressValidationService = e

	service.identityService = identityservice

	service.version = version

	service.testEnv = testEnv

	cwd, err := os.Getwd()
	if err != nil {
		log.Fatal(err)
	}
	service.staticPath = filepath.Join(cwd, "website")
	service.indexPath = "index.html"

	service.initializeSessions(cookieSecret)
	return
}

var (
	ipLimiters sync.Map // Stores *rate.Limiter for each IP address
)

// LimiterWithTimeout wraps a rate.Limiter with a cleanup timeout
type LimiterWithTimeout struct {
	limiter *rate.Limiter
	timer   *time.Timer
}

// Get or create a rate limiter for a specific IP address
func getIPLimiter(ip string) *rate.Limiter {
	// Load or create the limiter
	value, loaded := ipLimiters.Load(ip)
	if !loaded {
		// Create a new rate limiter with bucket size: 10
		limiter := rate.NewLimiter(rate.Every(time.Minute), 10)
		timer := time.AfterFunc(10*time.Minute, func() {
			ipLimiters.Delete(ip)
		})
		ipLimiters.Store(ip, &LimiterWithTimeout{limiter: limiter, timer: timer})
		return limiter
	}

	// Reset the cleanup timer
	limiterWithTimeout := value.(*LimiterWithTimeout)
	limiterWithTimeout.timer.Reset(10 * time.Minute)

	return limiterWithTimeout.limiter
}

// getClientIP extracts the client's IP address from the request
func getClientIP(r *http.Request) string {
	// Try to get the IP from the X-Forwarded-For header (if behind a proxy)
	ip := r.Header.Get("X-Forwarded-For")
	if ip != "" {
		// X-Forwarded-For can contain multiple IPs (e.g., "client, proxy1, proxy2")
		// Return the first one (the client's IP)
		return strings.Split(ip, ",")[0]
	}

	// Fall back to the remote address
	return strings.Split(r.RemoteAddr, ":")[0]
}

// RateLimitMiddleware enforces rate limiting per IP address
func RateLimitMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ip := getClientIP(r)
		if ip == "" {
			http.Error(w, "Unable to determine client IP", http.StatusBadRequest)
			return
		}

		// Get the rate limiter for the IP address
		limiter := getIPLimiter(ip)

		// Check if the client has exceeded the rate limit
		if !limiter.Allow() {
			http.Error(w, "Too many requests", http.StatusTooManyRequests)
			return
		}

		// Proceed to the next handler
		next.ServeHTTP(w, r)
	})
}

// RateLimitMiddleware checks if password login is enabled
func PasswordLoginMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Get DB session from the request
		session := db.GetDBSession(r)
		// Create a new manager for the OAuth service
		mgr := oauthservice.NewSessionManager(session)

		// Get password login status
		status, err := mgr.GetPasswordLoginStatus()
		if err != nil {
			log.Error("Error checking password login status:", err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}

		// Check if password login is disabled
		if status != nil && !status.Enabled {
			log.Debug("Password login is disabled")
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusForbidden)
			json.NewEncoder(w).Encode(map[string]string{
				"error": "Password login is disabled",
			})
			return
		}

		// Proceed to the next handler if password login is enabled
		next.ServeHTTP(w, r)
	})
}

// InitModels initialize persistance models
func (service *Service) InitModels() {
	service.initLoginModels()
}

func (service *Service) AddRoutes(router *mux.Router, CSRF func(http.Handler) http.Handler, dbmw func(http.Handler) http.Handler) {
	//Registration form
	router.Handle("/register", alice.New(CSRF, dbmw, RateLimitMiddleware, PasswordLoginMiddleware).Then(http.HandlerFunc(service.ProcessRegistrationForm))).Methods("POST")
	router.Handle("/register/emailconfirmed", alice.New(CSRF, dbmw).Then(http.HandlerFunc(service.CheckRegistrationEmailConfirmation))).Methods("GET")
	router.Handle("/register/smsconfirmation", alice.New(CSRF, dbmw).Then(http.HandlerFunc(service.ProcessPhonenumberConfirmationForm))).Methods("POST")
	router.Handle("/register/emailconfirmation", alice.New(CSRF, dbmw).Then(http.HandlerFunc(service.ProcessEmailConfirmationForm))).Methods("POST")
	router.Handle("/register/validation", alice.New(CSRF, dbmw, RateLimitMiddleware, PasswordLoginMiddleware).Then(http.HandlerFunc(service.ValidateInfo))).Methods("POST")
	router.Handle("/register/resendvalidation", alice.New(middleware.RateLimit(middleware.DefaultRateLimitPeriod, middleware.DefaultRateLimit).Handler).Then(http.HandlerFunc(service.ResendValidationInfo))).Methods("POST")
	//Enable us to "forget" users in case we are not in production
	router.Handle("/register/delete", alice.New(CSRF, dbmw).Then(http.HandlerFunc(service.ServeForgetAccountPage))).Methods("GET")
	router.Handle("/register/delete", alice.New(CSRF, dbmw).Then(http.HandlerFunc(service.ForgetAccountHandler))).Methods("POST")
	//Login forms
	router.Handle("/login", alice.New(CSRF, dbmw, RateLimitMiddleware, PasswordLoginMiddleware).Then(http.HandlerFunc(service.ProcessLoginForm))).Methods("POST")
	router.Handle("/login/twofamethods", alice.New(CSRF, dbmw, PasswordLoginMiddleware).Then(http.HandlerFunc(service.GetTwoFactorAuthenticationMethods))).Methods("GET")
	router.Handle("/login/totpconfirmation", alice.New(CSRF, dbmw, PasswordLoginMiddleware).Then(http.HandlerFunc(service.ProcessTOTPConfirmation))).Methods("POST")
	router.Handle("/login/smscode/{phoneLabel}", alice.New(middleware.RateLimit(middleware.DefaultRateLimitPeriod, middleware.DefaultRateLimit).Handler).Then(http.HandlerFunc(service.GetSmsCode))).Methods("POST")
	router.Handle("/login/smsconfirmation", alice.New(CSRF, dbmw).Then(http.HandlerFunc(service.Process2FASMSConfirmation))).Methods("POST")
	router.Handle("/login/resendsms", alice.New(middleware.RateLimit(middleware.DefaultRateLimitPeriod, middleware.DefaultRateLimit).Handler).Then(http.HandlerFunc(service.LoginResendPhonenumberConfirmation))).Methods("POST")
	router.Handle("/login/smsconfirmed", alice.New(CSRF, dbmw).Then(http.HandlerFunc(service.Check2FASMSConfirmation))).Methods("GET")
	router.Handle("/login/emailcode/{emailLabel}", alice.New(middleware.RateLimit(middleware.DefaultRateLimitPeriod, middleware.DefaultRateLimit).Handler).Then(http.HandlerFunc(service.GetEmailCode))).Methods("POST")
	router.Handle("/login/emailconfirmation", alice.New(CSRF, dbmw).Then(http.HandlerFunc(service.Process2FAEmailConfirmation))).Methods("POST")
	router.Handle("/login/resendemail", alice.New(middleware.RateLimit(middleware.DefaultRateLimitPeriod, middleware.DefaultRateLimit).Handler).Then(http.HandlerFunc(service.LoginResendEmailConfirmation))).Methods("POST")
	router.Handle("/login/validateemail", alice.New(CSRF, dbmw).Then(http.HandlerFunc(service.ValidateEmail))).Methods("POST")
	router.Handle("/login/forgotpassword", alice.New(CSRF, dbmw, RateLimitMiddleware, PasswordLoginMiddleware).Then(http.HandlerFunc(service.ForgotPassword))).Methods("POST")
	router.Handle("/login/resetpassword", alice.New(CSRF, dbmw, PasswordLoginMiddleware).Then(http.HandlerFunc(service.ResetPassword))).Methods("POST")
	router.Handle("/login/organizationinvitation/{code}", alice.New(CSRF, dbmw).Then(http.HandlerFunc(service.GetOrganizationInvitation))).Methods("GET")
	// OIDC routes
	router.Handle("/login/oidc", alice.New(dbmw).Then(http.HandlerFunc(service.ProcessOIDCLogin))).Methods("GET")
	router.Handle("/register/oidc", alice.New(dbmw).Then(http.HandlerFunc(service.GetOIDCRegisterInfo))).Methods("GET")
	router.Handle("/register/oidc", alice.New(CSRF, dbmw).Then(http.HandlerFunc(service.RegisterOIDCUser))).Methods("POST")
	//Logout link
	router.Methods("GET").Path("/logout").HandlerFunc(service.Logout)
	//Error page
	router.Methods("GET").Path("/config").HandlerFunc(service.GetConfig)

	router.Methods("GET").Path("/version").HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := struct {
			Version string
		}{
			Version: service.version,
		}
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(&response)
	})

	router.Methods("GET").Path("/location").HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Get the header from the cloudflare IP Geolocation service. Value is the
		// country code in ISO 3166-1 Alpha 2 format.
		location := r.Header.Get("CF-IPCountry")
		log.Debug("request location: ", location)
		response := struct {
			Location string `json:"location"`
		}{
			Location: location,
		}
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(&response)
	})
	router.Methods("POST").Path("/logs").HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		values := struct {
			Logs string `json:"logs"`
		}{}
		if err := json.NewDecoder(r.Body).Decode(&values); err != nil {
			log.Debug("Error decoding the logs body", err)
			http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
			return
		}
		username, err := service.GetLoggedInUser(r, w)
		if err != nil || username == "" {
			log.Debug("Error Getting logged in user:", err)
			http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
			return
		}
		message := fmt.Sprintf(`************** UI ERROR ****************
From: %s
Error:
%s
****************************************`, username, values.Logs)
		for _, messageLine := range strings.Split(message, "\n") {
			log.Error(messageLine)
		}
	})

	router.PathPrefix("/").Handler(alice.New(CSRF, dbmw).Then(http.HandlerFunc(service.ServeSPA)))
}

const (
	mainpageFileName      = "index.html"
	homepageFileName      = "base.html"
	errorpageFilename     = "error.html"
	apidocsPageFilename   = "apidocumentation.html"
	smsconfirmationPage   = "smsconfirmation.html"
	emailconfirmationPage = "emailconfirmation.html"
)

// ServeSPA serves the single page application and its assets
func (service *Service) ServeSPA(w http.ResponseWriter, request *http.Request) {
	log.Print("Serve SPA")
	// get the absolute path to prevent directory traversal
	urlPath, err := filepath.Abs(request.URL.Path)
	if err != nil {
		// if we failed to get the absolute path respond with a 400 bad request
		// and stop
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// prepend the path with the path to the static directory
	path := filepath.Join(service.staticPath, urlPath)
	log.Printf("Request is looking for path='%s'", path)

	// check whether a file exists at the given path
	_, err = os.Stat(path)
	if os.IsNotExist(err) || urlPath == "" || urlPath == "/" {
		// file does not exist, serve index.html
		path = filepath.Join(service.staticPath, service.indexPath)
		log.Printf("Serving %s", path)
		t := template.New(("SPA"))
		t, err = t.ParseFiles(path)
		if err != nil {
			log.Error("Could parse template: ", err)
			return
		}
		t.ExecuteTemplate(w, service.indexPath, map[string]interface{}{
			csrf.TemplateTag: csrf.TemplateField(request),
		})
		return
	} else if err != nil {
		// if we got an error (that wasn't that the file doesn't exist) stating the
		// file, return a 500 internal server error and stop
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// otherwise, use http.FileServer to serve the static dir
	http.FileServer(http.Dir(service.staticPath)).ServeHTTP(w, request)
}

// Logout logs out the user and redirect to the homepage
// TODO: csrf protection, really important here!
func (service *Service) Logout(w http.ResponseWriter, request *http.Request) {
	redirectUrl := request.URL.Query().Get("redirect")
	service.SetLoggedInUser(w, request, "")
	service.SetLoggedInOauthUser(w, request, "")
	sessions.Save(request, w)
	http.Redirect(w, request, redirectUrl, http.StatusFound)

}

func (service *Service) GetConfig(w http.ResponseWriter, request *http.Request) {
	token, err := totp.NewToken()
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	totpsession, err := service.GetSession(request, SessionForRegistration, "totp")
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	totpsession.Values["secret"] = token.Secret
	sessions.Save(request, w)
	data := struct {
		TotpIssuer string `json:"totpissuer"`
		TotpSecret string `json:"totpsecret"`
	}{
		TotpIssuer: totp.GetIssuer(request),
		TotpSecret: token.Secret,
	}
	json.NewEncoder(w).Encode(&data)
}
