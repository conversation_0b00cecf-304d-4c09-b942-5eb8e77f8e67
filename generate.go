package main

//This file contains the go:generate commands

// ## Embed the api specification
//go:generate go-bindata -pkg specifications -prefix specifications/api -o specifications/packaged.go specifications/api/...

//go-raml https://github.com/Jumpscale/go-raml server code generation from the RAML specification
//TODO: fix serverside code generation with go-raml

// ## API clients
//go:generate go-raml client -l go --dir clients/go/iam --ramlfile specifications/api/iam-gig-tech.raml --package iam

// ## Email templates ##
//go:generate go-bindata -pkg templates -prefix templates/templates -o templates/packaged/templates.go templates/templates/...
