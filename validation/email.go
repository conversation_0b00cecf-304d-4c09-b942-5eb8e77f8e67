package validation

import (
	"fmt"
	"net/http"
	"net/url"

	"git.gig.tech/gig-meneja/iam/config"
	"git.gig.tech/gig-meneja/iam/credentials/password"
	"git.gig.tech/gig-meneja/iam/db/validation"
	"git.gig.tech/gig-meneja/iam/identityservice/invitations"
	"git.gig.tech/gig-meneja/iam/tools"
	log "github.com/sirupsen/logrus"
)

const (
	emailWithButtonTemplateName  = "emailwithbutton.html"
	emailWithCodeTemplateName    = "emailwithcode.html"
	emailWithLinkTemplateName    = "emailwithlink.html"
	emailAlreadyUsedTemplateName = "emailalreadyused.html"
)

// EmailWithButtonTemplateParams structure
type EmailWithButtonTemplateParams struct {
	URLCaption string
	URL        string
	Username   string
	Title      string
	Text       string
	ButtonText string
	Reason     string
	Host       string
}

// EmailWithLinkTemplateParams structure
type EmailWithLinkTemplateParams struct {
	URL    string
	Title  string
	Text   string
	Reason string
	Host   string
}

// EmailWithCodeTemplateParams structure
type EmailWithCodeTemplateParams struct {
	Username string
	Title    string
	Text     string
	Reason   string
	Host     string
}

type AlreadyUsedEmailTemplateParams struct {
	Title      string
	IntroText  string
	LoginText  string
	ResetText  string
	ReportText string
	Host       string
}

// EmailService is the interface for an email communication channel, should be used by the IYOEmailAddressValidationService
type EmailService interface {
	Send(recipients []string, subject string, message string) (err error)
}

// IYOEmailAddressValidationService is the iam.gig.tech implementation of a EmailAddressValidationService
type IYOEmailAddressValidationService struct {
	EmailService EmailService
}

// RequestValidation validates the email address by sending an email
func (service *IYOEmailAddressValidationService) RequestValidation(request *http.Request, username string, email string, confirmationurl string, langKey string) (key string, err error) {
	valMngr := validation.NewManager(request)
	info, err := valMngr.NewEmailAddressValidationInformation(username, email)
	if err != nil {
		log.Error(err)
		return
	}
	err = valMngr.SaveEmailAddressValidationInformation(info)
	if err != nil {
		log.Error(err)
		return
	}

	translationValues := tools.TranslationValues{
		"emailvalidation_title": struct {
			Host string
		}{Host: request.Host},
		"emailvalidation_text": struct {
			Email string
			Code  string
			Host  string
		}{Email: email, Code: info.EmailCode, Host: request.Host},
		"emailvalidation_reason": struct {
			Host string
		}{Host: request.Host},
		"emailvalidation_subject": struct {
			Host string
		}{Host: request.Host},
	}

	translations, err := tools.ParseTranslations(langKey, translationValues)
	if err != nil {
		log.Error("Failed to parse translations: ", err)
		return
	}

	templateParameters := EmailWithCodeTemplateParams{
		Username: username,
		Title:    translations["emailvalidation_title"],
		Text:     translations["emailvalidation_text"],
		Reason:   translations["emailvalidation_reason"],
		Host:     request.Host,
	}
	message, err := tools.RenderTemplate(emailWithCodeTemplateName, templateParameters)
	if err != nil {
		return
	}

	go service.EmailService.Send([]string{email}, translations["emailvalidation_subject"], message)
	key = info.Key
	return
}

// RequestPasswordReset Request a password reset
func (service *IYOEmailAddressValidationService) RequestPasswordReset(request *http.Request, username string, emails []string, langKey string) (key string, err error) {
	pwdMngr := password.NewManager(request)
	token, err := pwdMngr.NewResetToken(username)
	if err != nil {
		return
	}
	if err = pwdMngr.SaveResetToken(token); err != nil {
		return
	}

	translationValues := tools.TranslationValues{
		"passwordreset_title": struct {
			Host string
		}{Host: request.Host},
		"passwordreset_text": struct {
			Host string
		}{Host: request.Host},
		"passwordreset_buttontext": nil,
		"passwordreset_reason": struct {
			Host string
		}{Host: request.Host},
		"passwordreset_subject": struct {
			Host string
		}{Host: request.Host},
		"passwordreset_URLCaption": nil,
	}

	translations, err := tools.ParseTranslations(langKey, translationValues)
	if err != nil {
		log.Error("Failed to parse translations: ", err)
		return
	}

	passwordreseturl := fmt.Sprintf("https://%s/resetpassword?lang=%s&token=%s", request.Host, langKey, url.QueryEscape(token.Token))
	templateParameters := EmailWithButtonTemplateParams{
		URLCaption: translations["passwordreset_URLCaption"],
		URL:        passwordreseturl,
		Username:   username,
		Title:      translations["passwordreset_title"],
		Text:       translations["passwordreset_text"],
		ButtonText: translations["passwordreset_buttontext"],
		Reason:     translations["passwordreset_reason"],
		Host:       request.Host,
	}
	message, err := tools.RenderTemplate(emailWithButtonTemplateName, templateParameters)
	if err != nil {
		return
	}
	go service.EmailService.Send(emails, translations["passwordreset_subject"], message)
	key = token.Token
	return
}

// SendOrganizationInviteEmail Sends an organization invite email
func (service *IYOEmailAddressValidationService) SendOrganizationInviteEmail(request *http.Request, invite *invitations.JoinOrganizationInvitation) (err error) {
	InviteURL := fmt.Sprintf(invitations.InviteURL, request.Host)
	templateParameters := EmailWithLinkTemplateParams{
		URL:    InviteURL,
		Title:  fmt.Sprintf("%s organization invitation", request.Host),
		Text:   fmt.Sprintf("You have been invited to the %s organization on %s. Click the link below to accept the invitation.", invite.Organization, request.Host),
		Reason: fmt.Sprintf("You’re receiving this email because someone invited you to an organization at %s. If you think this was a mistake please ignore this email.", request.Host),
		Host:   request.Host,
	}
	message, err := tools.RenderTemplate(emailWithLinkTemplateName, templateParameters)
	if err != nil {
		return
	}
	subject := fmt.Sprintf("You have been invited to the %s organization", invite.Organization)
	recipients := []string{invite.EmailAddress}
	go service.EmailService.Send(recipients, subject, message)
	return
}

// SendOrg2OrgInviteEmail Sends an organization (orgmember or orgowner) invite email to organization Owner
func (service *IYOEmailAddressValidationService) SendOrg2OrgInviteEmail(request *http.Request, invite *invitations.JoinOrganizationInvitation, email string) (err error) {
	InviteURL := fmt.Sprintf("https://%s/notifications", request.Host)
	templateParameters := EmailWithLinkTemplateParams{
		URL:    InviteURL,
		Title:  fmt.Sprintf("%s organization invitation", request.Host),
		Text:   fmt.Sprintf("Your organization %s has been invited to the %s organization as an %s on %s. You can accept or reject the invitation here:", invite.User, invite.Organization, invite.Role, request.Host),
		Reason: fmt.Sprintf("You’re receiving this email because someone invited an organization you are an owner of at %s. If you think this was a mistake please ignore this email.", request.Host),
		Host:   request.Host,
	}
	message, err := tools.RenderTemplate(emailWithLinkTemplateName, templateParameters)
	if err != nil {
		return
	}
	subject := fmt.Sprintf("Your organization %s has been invited to the %s organization as an %s on %s", invite.User, invite.Organization, invite.Role, request.Host)
	recipients := []string{email}
	go service.EmailService.Send(recipients, subject, message)
	return
}

// SendOrganizationInviteEmailCli Sends an organization invite email from cli
func (service *IYOEmailAddressValidationService) SendOrganizationInviteEmailCli(hostName string, invite *invitations.JoinOrganizationInvitation) (err error) {
	InviteURL := fmt.Sprintf(invitations.InviteURL, hostName)
	templateParameters := EmailWithLinkTemplateParams{
		URL:    InviteURL,
		Title:  fmt.Sprintf("%s organization invitation", hostName),
		Text:   fmt.Sprintf("You have been invited to the %s organization on %s. Click the link below to accept the invitation.", invite.Organization, hostName),
		Reason: fmt.Sprintf("You’re receiving this email because someone invited you to an organization at %s. If you think this was a mistake please ignore this email.", hostName),
		Host:   hostName,
	}
	message, err := tools.RenderTemplate(emailWithLinkTemplateName, templateParameters)
	if err != nil {
		return
	}
	subject := fmt.Sprintf("You have been invited to the %s organization", invite.Organization)
	recipients := []string{invite.EmailAddress}
	go service.EmailService.Send(recipients, subject, message)
	return
}

// ExpireValidation removes a pending validation
func (service *IYOEmailAddressValidationService) ExpireValidation(request *http.Request, key string) (err error) {
	if key == "" {
		return
	}
	valMngr := validation.NewManager(request)
	err = valMngr.RemoveEmailAddressValidationInformation(key)
	return
}

func (service *IYOEmailAddressValidationService) getEmailAddressValidationInformation(request *http.Request, key string) (info *validation.EmailAddressValidationInformation, err error) {
	if key == "" {
		return
	}
	valMngr := validation.NewManager(request)
	info, err = valMngr.GetByKeyEmailAddressValidationInformation(key)
	return
}

// IsConfirmed checks wether a validation request is already confirmed
func (service *IYOEmailAddressValidationService) IsConfirmed(request *http.Request, key string) (confirmed bool, err error) {
	info, err := service.getEmailAddressValidationInformation(request, key)
	if err != nil {
		return
	}
	if info == nil {
		err = ErrInvalidOrExpiredKey
		return
	}
	confirmed = info.Confirmed
	return
}

// ConfirmValidation checks if the supplied code matches the username and key
func (service *IYOEmailAddressValidationService) ConfirmValidation(request *http.Request, key, emailCode string) (err error) {
	info, err := service.getEmailAddressValidationInformation(request, key)
	if err != nil {
		return
	}
	if info == nil {
		err = ErrInvalidOrExpiredKey
		return
	}
	if info.EmailCode != emailCode {
		err = ErrInvalidCode
		return
	}
	valMngr := validation.NewManager(request)
	p := valMngr.NewValidatedEmailAddress(info.Username, info.EmailAddress)
	err = valMngr.SaveValidatedEmailAddress(p)
	if err != nil {
		return
	}
	err = valMngr.UpdateEmailAddressValidationInformation(key, true)
	if err != nil {
		return
	}
	return
}

// ConfirmRegistrationValidation checks if the supplied code matches the username and key. It does not add an entry
// in the validated email addresses collection
func (service *IYOEmailAddressValidationService) ConfirmRegistrationValidation(r *http.Request, key, emailCode string) (err error) {
	info, err := service.getEmailAddressValidationInformation(r, key)
	if err != nil {
		return
	}
	if info == nil {
		err = ErrInvalidOrExpiredKey
		return
	}
	if info.EmailCode != emailCode {
		err = ErrInvalidCode
		return
	}
	return validation.NewManager(r).UpdateEmailAddressValidationInformation(key, true)
}

func (service *IYOEmailAddressValidationService) Send2FAEmail(r *http.Request, templateParameters EmailWithCodeTemplateParams, email string, subject string) (err error) {
	message, err := tools.RenderTemplate(emailWithCodeTemplateName, templateParameters)
	if err != nil {
		return
	}
	go service.EmailService.Send([]string{email}, subject, message)
	return
}

// NotifyEmailAlreadyUsed Notify user that email is already used
func (service *IYOEmailAddressValidationService) NotifyEmailAlreadyUsed(host string, email string, langKey string) (err error) {

	supportEmail := config.SupportEmail

	translationValues := tools.TranslationValues{
		"email_already_used_title":       struct{ Host string }{Host: host},
		"email_already_used_intro_text":  struct{ Email, Host string }{Email: email, Host: host},
		"email_already_used_login_text":  struct{}{},
		"email_already_used_reset_text":  struct{}{},
		"email_already_used_report_text": struct{ Email string }{Email: supportEmail},
	}

	translations, err := tools.ParseTranslations(langKey, translationValues)
	if err != nil {
		log.Error("Failed to parse translations: ", err)
		return
	}

	templateParameters := AlreadyUsedEmailTemplateParams{
		Title:      translations["email_already_used_title"],
		IntroText:  translations["email_already_used_intro_text"],
		LoginText:  translations["email_already_used_login_text"],
		ResetText:  translations["email_already_used_reset_text"],
		ReportText: translations["email_already_used_report_text"],
		Host:       host,
	}
	message, err := tools.RenderTemplate(emailAlreadyUsedTemplateName, templateParameters)
	if err != nil {
		return
	}

	go service.EmailService.Send([]string{email}, translations["email_already_used_title"], message)
	return
}
