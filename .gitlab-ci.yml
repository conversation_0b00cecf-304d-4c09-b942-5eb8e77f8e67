image: geerlingguy/docker-ubuntu1804-ansible:latest

include:
- project: 'gig-meneja/infrastructure'
  ref: master
  file: '/.gig-devops-template.yml'

stages:
- test
- build
- qas_deploy
- prd_deploy

unittest:
  stage: test
  image: golang:1.22
  script:
  - set -e
  - git clone https://gitlab-ci-token:${CI_JOB_TOKEN}@git.gig.tech/gig-meneja/forks/go-raml.git $GOPATH/src/github.com/Jumpscale/go-raml
  - pushd $GOPATH/src/github.com/Jumpscale/go-raml
  - git checkout a8cc1c065538cfc97ffc077d3a185f653ad406e6
  - ./build.sh
  - popd
  - mkdir -p $GOPATH/src/git.gig.tech/gig-meneja/iam
  - cp -ar * $GOPATH/src/git.gig.tech/gig-meneja/iam
  - cd $GOPATH/src/git.gig.tech/gig-meneja/iam
  - bash builddev.sh
  - go test -v ./...
lint:
  stage: test
  image: golang:1.22
  allow_failure: true
  script:
    - set -e
    - go install golang.org/x/lint/golint@latest
    - export PATH=$PATH:$(go env GOPATH)/bin      # Add GOPATH/bin to PATH
    - for f in $(find . -name "*.go" | grep -v "^./vendor.*" | grep -v "^./clients.*"); do golint "${f}"; done
build:
  stage: build
  image: docker:stable
  script:
  - registry_login
  - docker build --pull --tag $CI_REGISTRY/$CI_PROJECT_PATH:$CI_COMMIT_REF_NAME --build-arg TOKEN=$CI_JOB_TOKEN .
  - docker tag $CI_REGISTRY/$CI_PROJECT_PATH:$CI_COMMIT_REF_NAME $CI_REGISTRY/$CI_PROJECT_PATH:$CI_COMMIT_SHA
  - docker push $CI_REGISTRY/$CI_PROJECT_PATH:$CI_COMMIT_REF_NAME
  - docker push $CI_REGISTRY/$CI_PROJECT_PATH:$CI_COMMIT_SHA

.deploy_template: &deploy
  image: ghub.gig.tech/gig-meneja/meneja/meneja-deploy:master
  only:
    refs:
      - master  
  script:
    - setup_kubeconfig
    - |
      if [[ "$QA_BRANCH" != "master" && "$CI_ENVIRONMENT_NAME" == "qas" ]]; then
        echo ${!qa_kubeconfig_name} | base64 -d > $KUBECONFIG
      fi
    - sed -i -e "s/{{ DEPLOY_TIMESTAMP }}/$(date +%s)/" kubernetes/iam-vco-config.yaml
    - sed -i -e "s/{{ IAM_IMAGE_TAG }}/$CI_COMMIT_SHA/" kubernetes/iam-vco-config.yaml
    - kubectl apply --validate -f kubernetes/iam-vco-config.yaml

qas_deploy:
  <<: *deploy
  stage: qas_deploy
  when: manual
  environment:
    name: qas
  except:
    - schedules    

prd_deploy:
  <<: *deploy
  stage: prd_deploy
  environment:
    name: prd
  when: manual
